﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-ekb-config
  namespace: imip-ekb-dev
data:
  ASPNETCORE_ENVIRONMENT: "Development"
  App__SelfUrl: "${DEV_APP_URL}"
  App__ServerRootAddress: "${DEV_APP_URL}"
  App__ClientUrl: "${DEV_CLIENT_URL}"
  App__CorsOrigins: "${DEV_CORS_ORIGINS}"
  App__HealthCheckUrl: "/api/health/kubernetes"
  App__HealthUiCheckUrl: "${DEV_APP_URL}/api/health/kubernetes"
  Seq__ServerUrl: "${SEQ_SERVER_URL}"
  AuthServer__Authority: "${DEV_AUTH_APP_URL}"
  AuthServer__ClientId: "${DEV_AUTH_CLIENT_ID}"
  AuthServer__ClientSecret: "${DEV_AUTH_CLIENT_SECRET}"
  AuthServer__RequireHttpsMetadata: "false"
  OpenIdConnect__Authority: "${DEV_AUTH_APP_URL}"
  OpenIdConnect__ClientId: "${DEV_AUTH_CLIENT_ID}"
  OpenIdConnect__ClientSecret: "${DEV_AUTH_CLIENT_SECRET}"
  OpenIdConnect__RequireHttpsMetadata: "false"
  OpenIdConnect__ResponseType: "code"
  OpenIdConnect__UsePkce: "true"
  OpenIdConnect__SaveTokens: "true"
  OpenIdConnect__GetClaimsFromUserInfoEndpoint: "true"
  OpenIdConnect__Scopes__0: "openid"
  OpenIdConnect__Scopes__1: "profile"
  OpenIdConnect__Scopes__2: "email"
  OpenIdConnect__PostLogoutRedirectUri: "${DEV_APP_URL}/signout-callback-oidc"
  OpenIdConnect__SignedOutRedirectUri: "${DEV_APP_URL}"
  OpenIdConnect__RedirectUri: "${DEV_APP_URL}/signin-oidc"
  OpenIdConnect__ForceHttpsForExternalDomains: "true"
  OpenIdConnect__EnableHttpsWorkaround: "true"
  OpenIdConnect__ForceHttpsDomains__0: "ekb-dev.imip.co.id"
  OpenIdConnect__ForceHttpsDomains__1: "ekb.imip.co.id"
  OpenIddict__Applications__Ekb_App__ClientId: "EkbLocal"
  OpenIddict__Applications__Ekb_App__RootUrl: "${DEV_APP_URL}"
  OpenIddict__Applications__Ekb_Swagger__ClientId: "Ekb_Swagger"
  OpenIddict__Applications__Ekb_Swagger__RootUrl: "${DEV_APP_URL}"
  ExternalAuth__ApiUrl: "${DEV_EXTERNAL_AUTH_URL}"
  ExternalAuth__Enabled: "true"
  AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
  Redis__IsEnabled: "true"
  DocumentConversion__MaxConcurrentConversions: "2"
  PdfOptimization__EmbedFonts: "false"
  PdfOptimization__OptimizeIdenticalImages: "true"
  PdfOptimization__CompressionLevel: "Best"
  PdfOptimization__MaxFileSizeWarningMB: "5.0"
  PdfOptimization__MaxFileSizeForPostProcessingMB: "2.0"
  PdfOptimization__UseMinimalFonts: "true"
  PdfOptimization__DefaultFontFamily: "Arial"
  PdfOptimization__UseLighterFontWeight: "true"
  Clock__Kind: "Local"
  Redis__Configuration: "**********:6379"
  DataProtection__ForceRedis: "true"
  # User Synchronization Configuration for Development
  UserSynchronization__IsEnabled: "${DEV_USER_SYNC_ENABLED}"
  UserSynchronization__UpdateExistingUsers: "${DEV_USER_SYNC_UPDATE_EXISTING}"
  UserSynchronization__SynchronizeRoles: "${DEV_USER_SYNC_ROLES}"
  UserSynchronization__SynchronizeClaims: "${DEV_USER_SYNC_CLAIMS}"
  UserSynchronization__EnableLogging: "${DEV_USER_SYNC_LOGGING}"
