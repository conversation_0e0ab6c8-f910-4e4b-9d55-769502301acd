using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Shouldly;
using Xunit;

namespace Imip.Ekb.Controllers;

[Collection(EkbTestConsts.CollectionDefinitionName)]
public class AgentControllerTests : EkbWebTestBase
{
    [Fact]
    public async Task FilterList_WithoutBearerToken_ShouldReturn401()
    {
        // Arrange
        var requestBody = new
        {
            skipCount = 0,
            maxResultCount = 10
        };

        var content = new StringContent(
            JsonSerializer.Serialize(requestBody),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await Client.PostAsync("/api/ekb/agent/filter-list", content);

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.Unauthorized);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.ShouldNotBeNullOrEmpty();
        
        // Verify the error response format
        var errorResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
        errorResponse.GetProperty("error").GetProperty("code").GetString().ShouldBe("UNAUTHORIZED");
        errorResponse.GetProperty("error").GetProperty("message").GetString().ShouldBe("Authentication failed");
        errorResponse.GetProperty("path").GetString().ShouldBe("/api/ekb/agent/filter-list");
    }

    [Fact]
    public async Task FilterList_WithInvalidBearerToken_ShouldReturn401()
    {
        // Arrange
        var requestBody = new
        {
            skipCount = 0,
            maxResultCount = 10
        };

        var content = new StringContent(
            JsonSerializer.Serialize(requestBody),
            Encoding.UTF8,
            "application/json");

        // Add invalid bearer token
        Client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "invalid-token");

        // Act
        var response = await Client.PostAsync("/api/ekb/agent/filter-list", content);

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.Unauthorized);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.ShouldNotBeNullOrEmpty();
        
        // Verify the error response format
        var errorResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
        errorResponse.GetProperty("error").GetProperty("code").GetString().ShouldBe("UNAUTHORIZED");
        errorResponse.GetProperty("error").GetProperty("message").GetString().ShouldBe("Authentication failed");
    }

    [Fact]
    public async Task GetList_WithoutBearerToken_ShouldReturn401()
    {
        // Act
        var response = await Client.GetAsync("/api/ekb/agent");

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.Unauthorized);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.ShouldNotBeNullOrEmpty();
        
        // Verify the error response format
        var errorResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
        errorResponse.GetProperty("error").GetProperty("code").GetString().ShouldBe("UNAUTHORIZED");
        errorResponse.GetProperty("error").GetProperty("message").GetString().ShouldBe("Authentication failed");
        errorResponse.GetProperty("path").GetString().ShouldBe("/api/ekb/agent");
    }

    [Fact]
    public async Task HealthCheck_ShouldNotRequireAuthentication()
    {
        // Act
        var response = await Client.GetAsync("/api/health/kubernetes");

        // Assert
        response.StatusCode.ShouldBe(HttpStatusCode.OK);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.ShouldNotBeNullOrEmpty();
        
        // Verify the health check response
        var healthResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
        healthResponse.GetProperty("status").GetString().ShouldBe("healthy");
    }
}
