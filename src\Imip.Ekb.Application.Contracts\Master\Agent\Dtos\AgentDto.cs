using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Agent.Dtos;

public class AgentDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Type { get; set; }
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? NpwpNo { get; set; }
    public string? BdmSapcode { get; set; }
    public string? TaxCode { get; set; }
    public string? AddressNpwp { get; set; }
    public string? Address { get; set; }
    public string? SapcodeS4 { get; set; }
}