﻿using Imip.Ekb.Master.Trading;
using Imip.Ekb.Master.Trading.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class TradingMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Trading.Id), nameof(TradingDto.Id))]
    public partial TradingDto MapToDto(Trading entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(Trading.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(Trading.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(Trading.CreatedAt))]
    public partial void MapToEntity(TradingCreateUpdateDto dto, Trading entity);

    // Custom mapping methods for complex scenarios
    public Trading CreateEntityWithId(TradingCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Trading)Activator.CreateInstance(typeof(Trading), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<TradingDto> MapToDtoList(List<Trading> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<TradingDto> MapToDtoEnumerable(IEnumerable<Trading> entities);
}
