using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.DataProtection;

namespace Imip.Ekb.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ILogger<TestController> _logger;
    private readonly IDataProtectionProvider _dataProtectionProvider;

    public TestController(ILogger<TestController> logger, IDataProtectionProvider dataProtectionProvider)
    {
        _logger = logger;
        _dataProtectionProvider = dataProtectionProvider;
    }

    [HttpGet("headers")]
    public IActionResult GetHeaders()
    {
        var headers = new
        {
            Scheme = Request.Scheme,
            Host = Request.Host.Value,
            XForwardedProto = Request.Headers["X-Forwarded-Proto"].ToString(),
            XForwardedHost = Request.Headers["X-Forwarded-Host"].ToString(),
            XForwardedFor = Request.Headers["X-Forwarded-For"].ToString(),
            XRealIP = Request.Headers["X-Real-IP"].ToString(),
            UserAgent = Request.Headers["User-Agent"].ToString()
        };

        _logger.LogWarning("Headers test - Scheme: {Scheme}, Host: {Host}, X-Forwarded-Proto: {XForwardedProto}",
            headers.Scheme, headers.Host, headers.XForwardedProto);

        return Ok(headers);
    }

    [HttpGet("dataprotection")]
    public IActionResult TestDataProtection()
    {
        try
        {
            var protector = _dataProtectionProvider.CreateProtector("test");
            var originalText = "test-data-protection";
            var protectedText = protector.Protect(originalText);
            var unprotectedText = protector.Unprotect(protectedText);

            var result = new
            {
                Success = true,
                OriginalText = originalText,
                ProtectedText = protectedText,
                UnprotectedText = unprotectedText,
                IsWorking = originalText == unprotectedText
            };

            _logger.LogWarning("Data protection test - Success: {Success}, IsWorking: {IsWorking}",
                result.Success, result.IsWorking);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Data protection test failed");
            return BadRequest(new { Success = false, Error = ex.Message });
        }
    }
}