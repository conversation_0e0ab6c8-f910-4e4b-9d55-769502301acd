using Imip.Ekb.Models;
using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.Dtos;

public class VesselListRequestDto : PagedAndSortedResultRequestDto
{
    public string? VesselType { get; set; } // "Import", "Export", "LocalIn", "LocalOut"
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public string? TenantName { get; set; }
    public FilterGroup? FilterGroup { get; set; }
}