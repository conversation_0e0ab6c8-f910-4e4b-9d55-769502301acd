using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Master.BusinessPartner.Dtos;

public class BusinessPartnerCreateUpdateDto
{
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string CreatedBy { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Status { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Alias { get; set; }

    [StringLength(255)]
    public string? Image { get; set; }

    [StringLength(200)]
    public string? Direction { get; set; }

    [Required]
    [StringLength(200)]
    public string RegionType { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Address { get; set; }

    [StringLength(255)]
    public string? Tenant { get; set; }

    [StringLength(255)]
    public string? Npwp1 { get; set; }

    [StringLength(255)]
    public string? Npwp2 { get; set; }
}