using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Cargo.Dtos;

public class CargoDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Alias { get; set; }
    public string? Flag { get; set; }
    public decimal GrossWeight { get; set; }
    public string? Type { get; set; }
    public decimal? LoaQty { get; set; }
}