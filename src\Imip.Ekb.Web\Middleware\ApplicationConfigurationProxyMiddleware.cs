using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Imip.Ekb.Web.Extensions;

namespace Imip.Ekb.Web.Middleware;

/// <summary>
/// Middleware that intercepts requests to /api/abp/application-configuration 
/// and proxies them to the identity server
/// </summary>
public class ApplicationConfigurationProxyMiddleware : IMiddleware, ITransientDependency
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ApplicationConfigurationProxyMiddleware> _logger;

    public ApplicationConfigurationProxyMiddleware(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ApplicationConfigurationProxyMiddleware> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Check if this is a request to the application configuration endpoint
        if (context.Request.Path.StartsWithSegments("/api/abp/application-configuration"))
        {
            _logger.LogDebug("Intercepting application configuration request: {Path}", context.Request.Path);

            await HandleApplicationConfigurationRequestAsync(context);
            return;
        }

        // Continue with the next middleware
        await next(context);
    }

    private async Task HandleApplicationConfigurationRequestAsync(HttpContext context)
    {
        try
        {
            // Get the access token from the current request
            var accessToken = await context.GetTokenAsync("access_token");

            // If not found in authentication properties, try Authorization header
            if (string.IsNullOrEmpty(accessToken))
            {
                var authHeader = context.Request.Headers.Authorization.FirstOrDefault();
                if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                {
                    accessToken = authHeader["Bearer ".Length..].Trim();
                }
            }

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No access token found for application configuration request");
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync("{\"error\":\"Access token required\"}");
                return;
            }

            var identityServerUrl = _configuration["AuthServer:Authority"];
            if (string.IsNullOrEmpty(identityServerUrl))
            {
                _logger.LogError("AuthServer:Authority not configured");
                context.Response.StatusCode = 500;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync("{\"error\":\"Identity server not configured\"}");
                return;
            }

            // Build the target URL with query parameters
            var queryString = context.Request.QueryString.ToString();
            var endpoint = $"{identityServerUrl}/api/abp/application-configuration{queryString}";

            _logger.LogDebug("Proxying application configuration request to: {Endpoint}", endpoint);

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            client.Timeout = TimeSpan.FromSeconds(30);

            var response = await client.GetAsync(endpoint);

            // Copy the response status code
            context.Response.StatusCode = (int)response.StatusCode;

            // Copy response headers (except some that shouldn't be copied)
            foreach (var header in response.Headers)
            {
                if (!ShouldSkipHeader(header.Key))
                {
                    context.Response.Headers[header.Key] = header.Value.ToArray();
                }
            }

            foreach (var header in response.Content.Headers)
            {
                if (!ShouldSkipHeader(header.Key))
                {
                    context.Response.Headers[header.Key] = header.Value.ToArray();
                }
            }

            // Copy the response body
            var content = await response.Content.ReadAsStringAsync();
            await context.Response.WriteAsync(content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogDebug("Successfully proxied application configuration from identity server");
            }
            else
            {
                _logger.LogWarning("Identity server returned status code {StatusCode} for application configuration request",
                    response.StatusCode);
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while requesting application configuration from identity server");
            context.Response.StatusCode = 502;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync($"{{\"error\":\"Failed to connect to identity server\",\"details\":\"{ex.Message}\"}}");
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while requesting application configuration from identity server");
            context.Response.StatusCode = 504;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync("{\"error\":\"Timeout connecting to identity server\"}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while requesting application configuration from identity server");
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync($"{{\"error\":\"Internal server error\",\"details\":\"{ex.Message}\"}}");
        }
    }

    private static bool ShouldSkipHeader(string headerName)
    {
        // Skip headers that shouldn't be copied from the proxied response
        var headersToSkip = new[]
        {
            "transfer-encoding",
            "connection",
            "upgrade",
            "proxy-authenticate",
            "proxy-authorization",
            "te",
            "trailers",
            "server"
        };

        return headersToSkip.Contains(headerName.ToLowerInvariant());
    }
}

/// <summary>
/// Extension methods for ApplicationConfigurationProxyMiddleware
/// </summary>
public static class ApplicationConfigurationProxyMiddlewareExtensions
{
    /// <summary>
    /// Adds the application configuration proxy middleware to the pipeline
    /// </summary>
    /// <param name="builder">The application builder</param>
    /// <returns>The application builder</returns>
    public static IApplicationBuilder UseApplicationConfigurationProxy(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ApplicationConfigurationProxyMiddleware>();
    }
}
