﻿apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: imip-ekb-ingress
  namespace: imip-ekb-prod
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "256k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "16"
    nginx.ingress.kubernetes.io/proxy-busy-buffers-size: "512k"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/client-header-timeout: "120"
    nginx.ingress.kubernetes.io/client-body-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout http_500 http_502 http_503 http_504"
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "3"
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "120"
    # Force HTTPS and proper header forwarding
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto https;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header Host $host;
      proxy_set_header X-Forwarded-Port 443;
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - ekb.imip.co.id
      secretName: imip-ekb-tls
  rules:
    - host: ekb.imip.co.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: imip-ekb-web
                port:
                  number: 80
