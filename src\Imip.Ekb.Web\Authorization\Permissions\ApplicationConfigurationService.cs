using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace Imip.Ekb.Web.Authorization.Permissions;

public class ApplicationConfigurationService : ITransientDependency
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ApplicationConfigurationService> _logger;

    public ApplicationConfigurationService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ApplicationConfigurationService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    // Cache for granted policies
    private Dictionary<string, bool> _grantedPoliciesCache = new Dictionary<string, bool>();
    private DateTime _grantedPoliciesCacheExpiration = DateTime.MinValue;
    private readonly object _cacheLock = new object();
    private readonly TimeSpan _cacheDuration = TimeSpan.FromMinutes(5); // Cache for 5 minutes

    public async Task<Dictionary<string, bool>> GetGrantedPoliciesAsync(string accessToken)
    {
        // Check if we have a valid cache
        if (_grantedPoliciesCache.Count > 0 && DateTime.UtcNow < _grantedPoliciesCacheExpiration)
        {
            _logger.LogDebug("Using cached granted policies ({Count} policies)", _grantedPoliciesCache.Count);
            return new Dictionary<string, bool>(_grantedPoliciesCache); // Return a copy to prevent modification
        }

        try
        {
            var identityServerUrl = _configuration["AuthServer:Authority"];
            var appConfigEndpoint = $"{identityServerUrl}/api/abp/application-configuration";

            _logger.LogDebug("Getting application configuration from: {Endpoint}", appConfigEndpoint);

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            // Set a reasonable timeout
            client.Timeout = TimeSpan.FromSeconds(30);

            // Add fallback DNS resolution for known hosts
            var knownHosts = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { "api-identity-dev.imip.co.id", "**********" },
                { "api-identity.imip.co.id", "**********" }
            };

            // Extract hostname from URL
            if (!string.IsNullOrEmpty(identityServerUrl))
            {
                try
                {
                    var uri = new Uri(identityServerUrl);
                    var hostname = uri.Host;

                    // Check if we need to use a fallback IP
                    if (knownHosts.TryGetValue(hostname, out var fallbackIp))
                    {
                        _logger.LogDebug("Using fallback IP {FallbackIp} for hostname {Hostname}", fallbackIp,
                            hostname);

                        // Create a modified URL with the IP address instead of the hostname
                        var scheme = uri.Scheme;
                        var port = uri.Port > 0 && uri.Port != 80 && uri.Port != 443 ? $":{uri.Port}" : "";
                        var path = uri.AbsolutePath.TrimEnd('/');

                        var fallbackUrl = $"{scheme}://{fallbackIp}{port}{path}/api/abp/application-configuration";
                        _logger.LogDebug("Fallback URL: {FallbackUrl}", fallbackUrl);

                        try
                        {
                            // Try with the fallback URL first
                            var fallbackResponse = await client.GetAsync(fallbackUrl);
                            if (fallbackResponse.IsSuccessStatusCode)
                            {
                                _logger.LogDebug("Successfully used fallback URL");
                                var content = await fallbackResponse.Content.ReadAsStringAsync();

                                // Parse the JSON response
                                using var document = JsonDocument.Parse(content);

                                // Navigate to the auth.grantedPolicies property
                                if (document.RootElement.TryGetProperty("auth", out var authElement) &&
                                    authElement.TryGetProperty("grantedPolicies", out var grantedPoliciesElement))
                                {
                                    var grantedPolicies = new Dictionary<string, bool>();

                                    // Enumerate all properties in the grantedPolicies object
                                    foreach (var property in grantedPoliciesElement.EnumerateObject())
                                    {
                                        grantedPolicies[property.Name] = property.Value.GetBoolean();
                                    }

                                    _logger.LogDebug("Retrieved {Count} granted policies from Identity Server",
                                        grantedPolicies.Count);

                                    // Update the cache
                                    lock (_cacheLock)
                                    {
                                        _grantedPoliciesCache = new Dictionary<string, bool>(grantedPolicies);
                                        _grantedPoliciesCacheExpiration = DateTime.UtcNow.Add(_cacheDuration);
                                    }

                                    return grantedPolicies;
                                }
                            }

                            _logger.LogWarning("Fallback URL failed with status code {StatusCode}, trying original URL",
                                fallbackResponse.StatusCode);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error using fallback URL, trying original URL");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error parsing Identity Server URL, trying original URL");
                }
            }

            // Try with the original URL
            var response = await client.GetAsync(appConfigEndpoint);

            _logger.LogDebug("Response status code: {StatusCode}", response.StatusCode);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();

                // Parse the JSON response
                using var document = JsonDocument.Parse(content);

                // Navigate to the auth.grantedPolicies property
                if (document.RootElement.TryGetProperty("auth", out var authElement))
                {
                    if (authElement.TryGetProperty("grantedPolicies", out var grantedPoliciesElement))
                    {
                        var grantedPolicies = new Dictionary<string, bool>();

                        // Enumerate all properties in the grantedPolicies object
                        foreach (var property in grantedPoliciesElement.EnumerateObject())
                        {
                            grantedPolicies[property.Name] = property.Value.GetBoolean();
                        }

                        _logger.LogDebug("Retrieved {Count} granted policies from Identity Server",
                            grantedPolicies.Count);

                        // Update the cache
                        lock (_cacheLock)
                        {
                            _grantedPoliciesCache = new Dictionary<string, bool>(grantedPolicies);
                            _grantedPoliciesCacheExpiration = DateTime.UtcNow.Add(_cacheDuration);
                        }

                        return grantedPolicies;
                    }
                    else
                    {
                        _logger.LogWarning("Could not find grantedPolicies in the auth element");
                    }
                }
                else
                {
                    _logger.LogWarning("Could not find auth element in the response");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get application configuration. Status: {Status}", response.StatusCode);

                // Try to read the error response
                try
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Error response: {ErrorContent}", errorContent);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Could not read error response: {ErrorMessage}", ex.Message);
                }
            }

            // If we get here, we couldn't get the policies from the Identity Server
            // Check if we have a cache, even if it's expired
            if (_grantedPoliciesCache.Count > 0)
            {
                _logger.LogWarning("Using expired cached granted policies as fallback");
                return new Dictionary<string, bool>(_grantedPoliciesCache);
            }

            // If we have no cache, return an empty dictionary
            return new Dictionary<string, bool>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting granted policies from Identity Server");

            // If we have a cache, even if it's expired, use it as a fallback
            if (_grantedPoliciesCache.Count > 0)
            {
                _logger.LogWarning("Using expired cached granted policies as fallback after error");
                return new Dictionary<string, bool>(_grantedPoliciesCache);
            }

            return new Dictionary<string, bool>();
        }
    }
}
