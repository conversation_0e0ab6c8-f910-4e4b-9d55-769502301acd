import{r as Fs,g as js,a as c,j as m,R as Me,b as fo,c as $s}from"./vendor-B032F4SZ.js";var Ze=Fs();const Bs=js(Ze);function M(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}function $n(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function yt(...e){return t=>{let n=!1;const o=e.map(r=>{const s=$n(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():$n(e[r],null)}}}}function H(...e){return c.useCallback(yt(...e),e)}function Ws(e,t){const n=c.createContext(t),o=s=>{const{children:i,...a}=s,l=c.useMemo(()=>a,Object.values(a));return m.jsx(n.Provider,{value:l,children:i})};o.displayName=e+"Provider";function r(s){const i=c.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[o,r]}function xe(e,t=[]){let n=[];function o(s,i){const a=c.createContext(i),l=n.length;n=[...n,i];const u=d=>{const{scope:v,children:h,...w}=d,p=v?.[e]?.[l]||a,g=c.useMemo(()=>w,Object.values(w));return m.jsx(p.Provider,{value:g,children:h})};u.displayName=s+"Provider";function f(d,v){const h=v?.[e]?.[l]||a,w=c.useContext(h);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,f]}const r=()=>{const s=n.map(i=>c.createContext(i));return function(a){const l=a?.[e]||s;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return r.scopeName=e,[o,Hs(r,...t)]}function Hs(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const i=o.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Ae(e){const t=Vs(e),n=c.forwardRef((o,r)=>{const{children:s,...i}=o,a=c.Children.toArray(s),l=a.find(Us);if(l){const u=l.props.children,f=a.map(d=>d===l?c.Children.count(u)>1?c.Children.only(null):c.isValidElement(u)?u.props.children:null:d);return m.jsx(t,{...i,ref:r,children:c.isValidElement(u)?c.cloneElement(u,void 0,f):null})}return m.jsx(t,{...i,ref:r,children:s})});return n.displayName=`${e}.Slot`,n}var Ru=Ae("Slot");function Vs(e){const t=c.forwardRef((n,o)=>{const{children:r,...s}=n;if(c.isValidElement(r)){const i=Gs(r),a=Ks(s,r.props);return r.type!==c.Fragment&&(a.ref=o?yt(o,i):i),c.cloneElement(r,a)}return c.Children.count(r)>1?c.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var po=Symbol("radix.slottable");function Pu(e){const t=({children:n})=>m.jsx(m.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=po,t}function Us(e){return c.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===po}function Ks(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...a)=>{const l=s(...a);return r(...a),l}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function Gs(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function dn(e){const t=e+"CollectionProvider",[n,o]=xe(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=p=>{const{scope:g,children:x}=p,y=Me.useRef(null),S=Me.useRef(new Map).current;return m.jsx(r,{scope:g,itemMap:S,collectionRef:y,children:x})};i.displayName=t;const a=e+"CollectionSlot",l=Ae(a),u=Me.forwardRef((p,g)=>{const{scope:x,children:y}=p,S=s(a,x),C=H(g,S.collectionRef);return m.jsx(l,{ref:C,children:y})});u.displayName=a;const f=e+"CollectionItemSlot",d="data-radix-collection-item",v=Ae(f),h=Me.forwardRef((p,g)=>{const{scope:x,children:y,...S}=p,C=Me.useRef(null),b=H(g,C),I=s(f,x);return Me.useEffect(()=>(I.itemMap.set(C,{ref:C,...S}),()=>void I.itemMap.delete(C))),m.jsx(v,{[d]:"",ref:b,children:y})});h.displayName=f;function w(p){const g=s(e+"CollectionConsumer",p);return Me.useCallback(()=>{const y=g.collectionRef.current;if(!y)return[];const S=Array.from(y.querySelectorAll(`[${d}]`));return Array.from(g.itemMap.values()).sort((I,E)=>S.indexOf(I.ref.current)-S.indexOf(E.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:i,Slot:u,ItemSlot:h},w,o]}var zs=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],O=zs.reduce((e,t)=>{const n=Ae(`Primitive.${t}`),o=c.forwardRef((r,s)=>{const{asChild:i,...a}=r,l=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(l,{...a,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function mo(e,t){e&&Ze.flushSync(()=>e.dispatchEvent(t))}function se(e){const t=c.useRef(e);return c.useEffect(()=>{t.current=e}),c.useMemo(()=>(...n)=>t.current?.(...n),[])}function Ys(e,t=globalThis?.document){const n=se(e);c.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var Xs="DismissableLayer",Qt="dismissableLayer.update",qs="dismissableLayer.pointerDownOutside",Zs="dismissableLayer.focusOutside",Bn,vo=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Qe=c.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,u=c.useContext(vo),[f,d]=c.useState(null),v=f?.ownerDocument??globalThis?.document,[,h]=c.useState({}),w=H(t,E=>d(E)),p=Array.from(u.layers),[g]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=p.indexOf(g),y=f?p.indexOf(f):-1,S=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,b=Js(E=>{const A=E.target,D=[...u.branches].some(N=>N.contains(A));!C||D||(r?.(E),i?.(E),E.defaultPrevented||a?.())},v),I=ei(E=>{const A=E.target;[...u.branches].some(N=>N.contains(A))||(s?.(E),i?.(E),E.defaultPrevented||a?.())},v);return Ys(E=>{y===u.layers.size-1&&(o?.(E),!E.defaultPrevented&&a&&(E.preventDefault(),a()))},v),c.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Bn=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),Wn(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=Bn)}},[f,v,n,u]),c.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),Wn())},[f,u]),c.useEffect(()=>{const E=()=>h({});return document.addEventListener(Qt,E),()=>document.removeEventListener(Qt,E)},[]),m.jsx(O.div,{...l,ref:w,style:{pointerEvents:S?C?"auto":"none":void 0,...e.style},onFocusCapture:M(e.onFocusCapture,I.onFocusCapture),onBlurCapture:M(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:M(e.onPointerDownCapture,b.onPointerDownCapture)})});Qe.displayName=Xs;var Qs="DismissableLayerBranch",ho=c.forwardRef((e,t)=>{const n=c.useContext(vo),o=c.useRef(null),r=H(t,o);return c.useEffect(()=>{const s=o.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),m.jsx(O.div,{...e,ref:r})});ho.displayName=Qs;function Js(e,t=globalThis?.document){const n=se(e),o=c.useRef(!1),r=c.useRef(()=>{});return c.useEffect(()=>{const s=a=>{if(a.target&&!o.current){let l=function(){go(qs,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=l,t.addEventListener("click",r.current,{once:!0})):l()}else t.removeEventListener("click",r.current);o.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function ei(e,t=globalThis?.document){const n=se(e),o=c.useRef(!1);return c.useEffect(()=>{const r=s=>{s.target&&!o.current&&go(Zs,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Wn(){const e=new CustomEvent(Qt);document.dispatchEvent(e)}function go(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?mo(r,s):r.dispatchEvent(s)}var Mu=Qe,Au=ho,z=globalThis?.document?c.useLayoutEffect:()=>{},ti="Portal",St=c.forwardRef((e,t)=>{const{container:n,...o}=e,[r,s]=c.useState(!1);z(()=>s(!0),[]);const i=n||r&&globalThis?.document?.body;return i?Bs.createPortal(m.jsx(O.div,{...o,ref:t}),i):null});St.displayName=ti;function ni(e,t){return c.useReducer((n,o)=>t[n][o]??n,e)}var ye=e=>{const{present:t,children:n}=e,o=oi(t),r=typeof n=="function"?n({present:o.isPresent}):c.Children.only(n),s=H(o.ref,ri(r));return typeof n=="function"||o.isPresent?c.cloneElement(r,{ref:s}):null};ye.displayName="Presence";function oi(e){const[t,n]=c.useState(),o=c.useRef(null),r=c.useRef(e),s=c.useRef("none"),i=e?"mounted":"unmounted",[a,l]=ni(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return c.useEffect(()=>{const u=ot(o.current);s.current=a==="mounted"?u:"none"},[a]),z(()=>{const u=o.current,f=r.current;if(f!==e){const v=s.current,h=ot(u);e?l("MOUNT"):h==="none"||u?.display==="none"?l("UNMOUNT"):l(f&&v!==h?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,l]),z(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=h=>{const p=ot(o.current).includes(h.animationName);if(h.target===t&&p&&(l("ANIMATION_END"),!r.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},v=h=>{h.target===t&&(s.current=ot(o.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:c.useCallback(u=>{o.current=u?getComputedStyle(u):null,n(u)},[])}}function ot(e){return e?.animationName||"none"}function ri(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var si=fo[" useInsertionEffect ".trim().toString()]||z;function Be({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[r,s,i]=ii({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:r;{const f=c.useRef(e!==void 0);c.useEffect(()=>{const d=f.current;if(d!==a){const v=d?"controlled":"uncontrolled",h=a?"controlled":"uncontrolled"}f.current=a},[a,o])}const u=c.useCallback(f=>{if(a){const d=ai(f)?f(e):f;d!==e&&i.current?.(d)}else s(f)},[a,e,s,i]);return[l,u]}function ii({defaultProp:e,onChange:t}){const[n,o]=c.useState(e),r=c.useRef(n),s=c.useRef(t);return si(()=>{s.current=t},[t]),c.useEffect(()=>{r.current!==n&&(s.current?.(n),r.current=n)},[n,r]),[n,o,s]}function ai(e){return typeof e=="function"}var wo=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ci="VisuallyHidden",xo=c.forwardRef((e,t)=>m.jsx(O.span,{...e,ref:t,style:{...wo,...e.style}}));xo.displayName=ci;var Iu=xo,li=fo[" useId ".trim().toString()]||(()=>{}),ui=0;function ue(e){const[t,n]=c.useState(li());return z(()=>{n(o=>o??String(ui++))},[e]),e||(t?`radix-${t}`:"")}var Ct="Collapsible",[di,_u]=xe(Ct),[fi,fn]=di(Ct),yo=c.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:o,defaultOpen:r,disabled:s,onOpenChange:i,...a}=e,[l,u]=Be({prop:o,defaultProp:r??!1,onChange:i,caller:Ct});return m.jsx(fi,{scope:n,disabled:s,contentId:ue(),open:l,onOpenToggle:c.useCallback(()=>u(f=>!f),[u]),children:m.jsx(O.div,{"data-state":mn(l),"data-disabled":s?"":void 0,...a,ref:t})})});yo.displayName=Ct;var So="CollapsibleTrigger",pi=c.forwardRef((e,t)=>{const{__scopeCollapsible:n,...o}=e,r=fn(So,n);return m.jsx(O.button,{type:"button","aria-controls":r.contentId,"aria-expanded":r.open||!1,"data-state":mn(r.open),"data-disabled":r.disabled?"":void 0,disabled:r.disabled,...o,ref:t,onClick:M(e.onClick,r.onOpenToggle)})});pi.displayName=So;var pn="CollapsibleContent",mi=c.forwardRef((e,t)=>{const{forceMount:n,...o}=e,r=fn(pn,e.__scopeCollapsible);return m.jsx(ye,{present:n||r.open,children:({present:s})=>m.jsx(vi,{...o,ref:t,present:s})})});mi.displayName=pn;var vi=c.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:o,children:r,...s}=e,i=fn(pn,n),[a,l]=c.useState(o),u=c.useRef(null),f=H(t,u),d=c.useRef(0),v=d.current,h=c.useRef(0),w=h.current,p=i.open||a,g=c.useRef(p),x=c.useRef(void 0);return c.useEffect(()=>{const y=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(y)},[]),z(()=>{const y=u.current;if(y){x.current=x.current||{transitionDuration:y.style.transitionDuration,animationName:y.style.animationName},y.style.transitionDuration="0s",y.style.animationName="none";const S=y.getBoundingClientRect();d.current=S.height,h.current=S.width,g.current||(y.style.transitionDuration=x.current.transitionDuration,y.style.animationName=x.current.animationName),l(o)}},[i.open,o]),m.jsx(O.div,{"data-state":mn(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!p,...s,ref:f,style:{"--radix-collapsible-content-height":v?`${v}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:p&&r})});function mn(e){return e?"open":"closed"}var Tu=yo,Bt="focusScope.autoFocusOnMount",Wt="focusScope.autoFocusOnUnmount",Hn={bubbles:!1,cancelable:!0},hi="FocusScope",bt=c.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:s,...i}=e,[a,l]=c.useState(null),u=se(r),f=se(s),d=c.useRef(null),v=H(t,p=>l(p)),h=c.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;c.useEffect(()=>{if(o){let p=function(S){if(h.paused||!a)return;const C=S.target;a.contains(C)?d.current=C:Se(d.current,{select:!0})},g=function(S){if(h.paused||!a)return;const C=S.relatedTarget;C!==null&&(a.contains(C)||Se(d.current,{select:!0}))},x=function(S){if(document.activeElement===document.body)for(const b of S)b.removedNodes.length>0&&Se(a)};document.addEventListener("focusin",p),document.addEventListener("focusout",g);const y=new MutationObserver(x);return a&&y.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",g),y.disconnect()}}},[o,a,h.paused]),c.useEffect(()=>{if(a){Un.add(h);const p=document.activeElement;if(!a.contains(p)){const x=new CustomEvent(Bt,Hn);a.addEventListener(Bt,u),a.dispatchEvent(x),x.defaultPrevented||(gi(Ci(Co(a)),{select:!0}),document.activeElement===p&&Se(a))}return()=>{a.removeEventListener(Bt,u),setTimeout(()=>{const x=new CustomEvent(Wt,Hn);a.addEventListener(Wt,f),a.dispatchEvent(x),x.defaultPrevented||Se(p??document.body,{select:!0}),a.removeEventListener(Wt,f),Un.remove(h)},0)}}},[a,u,f,h]);const w=c.useCallback(p=>{if(!n&&!o||h.paused)return;const g=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,x=document.activeElement;if(g&&x){const y=p.currentTarget,[S,C]=wi(y);S&&C?!p.shiftKey&&x===C?(p.preventDefault(),n&&Se(S,{select:!0})):p.shiftKey&&x===S&&(p.preventDefault(),n&&Se(C,{select:!0})):x===y&&p.preventDefault()}},[n,o,h.paused]);return m.jsx(O.div,{tabIndex:-1,...i,ref:v,onKeyDown:w})});bt.displayName=hi;function gi(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(Se(o,{select:t}),document.activeElement!==n)return}function wi(e){const t=Co(e),n=Vn(t,e),o=Vn(t.reverse(),e);return[n,o]}function Co(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Vn(e,t){for(const n of e)if(!xi(n,{upTo:t}))return n}function xi(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function yi(e){return e instanceof HTMLInputElement&&"select"in e}function Se(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&yi(e)&&t&&e.select()}}var Un=Si();function Si(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Kn(e,t),e.unshift(t)},remove(t){e=Kn(e,t),e[0]?.resume()}}}function Kn(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function Ci(e){return e.filter(t=>t.tagName!=="A")}var Ht=0;function vn(){c.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Gn()),document.body.insertAdjacentElement("beforeend",e[1]??Gn()),Ht++,()=>{Ht===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Ht--}},[])}function Gn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var le=function(){return le=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},le.apply(this,arguments)};function bo(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function bi(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,s;o<r;o++)(s||!(o in t))&&(s||(s=Array.prototype.slice.call(t,0,o)),s[o]=t[o]);return e.concat(s||Array.prototype.slice.call(t))}var lt="right-scroll-bar-position",ut="width-before-scroll-bar",Ei="with-scroll-bars-hidden",Ri="--removed-body-scroll-bar-size";function Vt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Pi(e,t){var n=c.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}var Mi=typeof window<"u"?c.useLayoutEffect:c.useEffect,zn=new WeakMap;function Ai(e,t){var n=Pi(null,function(o){return e.forEach(function(r){return Vt(r,o)})});return Mi(function(){var o=zn.get(n);if(o){var r=new Set(o),s=new Set(e),i=n.current;r.forEach(function(a){s.has(a)||Vt(a,null)}),s.forEach(function(a){r.has(a)||Vt(a,i)})}zn.set(n,e)},[e]),n}function Ii(e){return e}function _i(e,t){t===void 0&&(t=Ii);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,o);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(o=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){o=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var f=i;i=[],f.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(f){i.push(f),u()},filter:function(f){return i=i.filter(f),n}}}};return r}function Ti(e){e===void 0&&(e={});var t=_i(null);return t.options=le({async:!0,ssr:!1},e),t}var Eo=function(e){var t=e.sideCar,n=bo(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return c.createElement(o,le({},n))};Eo.isSideCarExport=!0;function Oi(e,t){return e.useMedium(t),Eo}var Ro=Ti(),Ut=function(){},Et=c.forwardRef(function(e,t){var n=c.useRef(null),o=c.useState({onScrollCapture:Ut,onWheelCapture:Ut,onTouchMoveCapture:Ut}),r=o[0],s=o[1],i=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,f=e.enabled,d=e.shards,v=e.sideCar,h=e.noRelative,w=e.noIsolation,p=e.inert,g=e.allowPinchZoom,x=e.as,y=x===void 0?"div":x,S=e.gapMode,C=bo(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=v,I=Ai([n,t]),E=le(le({},C),r);return c.createElement(c.Fragment,null,f&&c.createElement(b,{sideCar:Ro,removeScrollBar:u,shards:d,noRelative:h,noIsolation:w,inert:p,setCallbacks:s,allowPinchZoom:!!g,lockRef:n,gapMode:S}),i?c.cloneElement(c.Children.only(a),le(le({},E),{ref:I})):c.createElement(y,le({},E,{className:l,ref:I}),a))});Et.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Et.classNames={fullWidth:ut,zeroRight:lt};var Ni=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Di(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ni();return t&&e.setAttribute("nonce",t),e}function Li(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function ki(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Fi=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Di())&&(Li(t,n),ki(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ji=function(){var e=Fi();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Po=function(){var e=ji(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},$i={left:0,top:0,right:0,gap:0},Kt=function(e){return parseInt(e||"",10)||0},Bi=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[Kt(n),Kt(o),Kt(r)]},Wi=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return $i;var t=Bi(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Hi=Po(),je="data-scroll-locked",Vi=function(e,t,n,o){var r=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ei,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(a,"px ").concat(o,`;
  }
  body[`).concat(je,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(lt,` {
    right: `).concat(a,"px ").concat(o,`;
  }
  
  .`).concat(ut,` {
    margin-right: `).concat(a,"px ").concat(o,`;
  }
  
  .`).concat(lt," .").concat(lt,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(ut," .").concat(ut,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(je,`] {
    `).concat(Ri,": ").concat(a,`px;
  }
`)},Yn=function(){var e=parseInt(document.body.getAttribute(je)||"0",10);return isFinite(e)?e:0},Ui=function(){c.useEffect(function(){return document.body.setAttribute(je,(Yn()+1).toString()),function(){var e=Yn()-1;e<=0?document.body.removeAttribute(je):document.body.setAttribute(je,e.toString())}},[])},Ki=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o;Ui();var s=c.useMemo(function(){return Wi(r)},[r]);return c.createElement(Hi,{styles:Vi(s,!t,r,n?"":"!important")})},Jt=!1;if(typeof window<"u")try{var rt=Object.defineProperty({},"passive",{get:function(){return Jt=!0,!0}});window.addEventListener("test",rt,rt),window.removeEventListener("test",rt,rt)}catch{Jt=!1}var Le=Jt?{passive:!1}:!1,Gi=function(e){return e.tagName==="TEXTAREA"},Mo=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Gi(e)&&n[t]==="visible")},zi=function(e){return Mo(e,"overflowY")},Yi=function(e){return Mo(e,"overflowX")},Xn=function(e,t){var n=t.ownerDocument,o=t;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var r=Ao(e,o);if(r){var s=Io(e,o),i=s[1],a=s[2];if(i>a)return!0}o=o.parentNode}while(o&&o!==n.body);return!1},Xi=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},qi=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},Ao=function(e,t){return e==="v"?zi(t):Yi(t)},Io=function(e,t){return e==="v"?Xi(t):qi(t)},Zi=function(e,t){return e==="h"&&t==="rtl"?-1:1},Qi=function(e,t,n,o,r){var s=Zi(e,window.getComputedStyle(t).direction),i=s*o,a=n.target,l=t.contains(a),u=!1,f=i>0,d=0,v=0;do{var h=Io(e,a),w=h[0],p=h[1],g=h[2],x=p-g-s*w;(w||x)&&Ao(e,a)&&(d+=x,v+=w),a=a.parentNode.host||a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(f&&Math.abs(d)<1||!f&&Math.abs(v)<1)&&(u=!0),u},st=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},qn=function(e){return[e.deltaX,e.deltaY]},Zn=function(e){return e&&"current"in e?e.current:e},Ji=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ea=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ta=0,ke=[];function na(e){var t=c.useRef([]),n=c.useRef([0,0]),o=c.useRef(),r=c.useState(ta++)[0],s=c.useState(Po)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var p=bi([e.lockRef.current],(e.shards||[]).map(Zn),!0).filter(Boolean);return p.forEach(function(g){return g.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),p.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var a=c.useCallback(function(p,g){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!i.current.allowPinchZoom;var x=st(p),y=n.current,S="deltaX"in p?p.deltaX:y[0]-x[0],C="deltaY"in p?p.deltaY:y[1]-x[1],b,I=p.target,E=Math.abs(S)>Math.abs(C)?"h":"v";if("touches"in p&&E==="h"&&I.type==="range")return!1;var A=Xn(E,I);if(!A)return!0;if(A?b=E:(b=E==="v"?"h":"v",A=Xn(E,I)),!A)return!1;if(!o.current&&"changedTouches"in p&&(S||C)&&(o.current=b),!b)return!0;var D=o.current||b;return Qi(D,g,p,D==="h"?S:C)},[]),l=c.useCallback(function(p){var g=p;if(!(!ke.length||ke[ke.length-1]!==s)){var x="deltaY"in g?qn(g):st(g),y=t.current.filter(function(b){return b.name===g.type&&(b.target===g.target||g.target===b.shadowParent)&&Ji(b.delta,x)})[0];if(y&&y.should){g.cancelable&&g.preventDefault();return}if(!y){var S=(i.current.shards||[]).map(Zn).filter(Boolean).filter(function(b){return b.contains(g.target)}),C=S.length>0?a(g,S[0]):!i.current.noIsolation;C&&g.cancelable&&g.preventDefault()}}},[]),u=c.useCallback(function(p,g,x,y){var S={name:p,delta:g,target:x,should:y,shadowParent:oa(x)};t.current.push(S),setTimeout(function(){t.current=t.current.filter(function(C){return C!==S})},1)},[]),f=c.useCallback(function(p){n.current=st(p),o.current=void 0},[]),d=c.useCallback(function(p){u(p.type,qn(p),p.target,a(p,e.lockRef.current))},[]),v=c.useCallback(function(p){u(p.type,st(p),p.target,a(p,e.lockRef.current))},[]);c.useEffect(function(){return ke.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:v}),document.addEventListener("wheel",l,Le),document.addEventListener("touchmove",l,Le),document.addEventListener("touchstart",f,Le),function(){ke=ke.filter(function(p){return p!==s}),document.removeEventListener("wheel",l,Le),document.removeEventListener("touchmove",l,Le),document.removeEventListener("touchstart",f,Le)}},[]);var h=e.removeScrollBar,w=e.inert;return c.createElement(c.Fragment,null,w?c.createElement(s,{styles:ea(r)}):null,h?c.createElement(Ki,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function oa(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ra=Oi(Ro,na);var Rt=c.forwardRef(function(e,t){return c.createElement(Et,le({},e,{ref:t,sideCar:ra}))});Rt.classNames=Et.classNames;var sa=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Fe=new WeakMap,it=new WeakMap,at={},Gt=0,_o=function(e){return e&&(e.host||_o(e.parentNode))},ia=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=_o(n);return o&&e.contains(o)?o:null}).filter(function(n){return!!n})},aa=function(e,t,n,o){var r=ia(t,Array.isArray(e)?e:[e]);at[n]||(at[n]=new WeakMap);var s=at[n],i=[],a=new Set,l=new Set(r),u=function(d){!d||a.has(d)||(a.add(d),u(d.parentNode))};r.forEach(u);var f=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(v){if(a.has(v))f(v);else try{var h=v.getAttribute(o),w=h!==null&&h!=="false",p=(Fe.get(v)||0)+1,g=(s.get(v)||0)+1;Fe.set(v,p),s.set(v,g),i.push(v),p===1&&w&&it.set(v,!0),g===1&&v.setAttribute(n,"true"),w||v.setAttribute(o,"true")}catch{}})};return f(t),a.clear(),Gt++,function(){i.forEach(function(d){var v=Fe.get(d)-1,h=s.get(d)-1;Fe.set(d,v),s.set(d,h),v||(it.has(d)||d.removeAttribute(o),it.delete(d)),h||d.removeAttribute(n)}),Gt--,Gt||(Fe=new WeakMap,Fe=new WeakMap,it=new WeakMap,at={})}},hn=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=sa(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live], script"))),aa(o,r,n,"aria-hidden")):function(){return null}},Pt="Dialog",[To,Ou]=xe(Pt),[ca,ce]=To(Pt),Oo=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:i=!0}=e,a=c.useRef(null),l=c.useRef(null),[u,f]=Be({prop:o,defaultProp:r??!1,onChange:s,caller:Pt});return m.jsx(ca,{scope:t,triggerRef:a,contentRef:l,contentId:ue(),titleId:ue(),descriptionId:ue(),open:u,onOpenChange:f,onOpenToggle:c.useCallback(()=>f(d=>!d),[f]),modal:i,children:n})};Oo.displayName=Pt;var No="DialogTrigger",la=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ce(No,n),s=H(t,r.triggerRef);return m.jsx(O.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":xn(r.open),...o,ref:s,onClick:M(e.onClick,r.onOpenToggle)})});la.displayName=No;var gn="DialogPortal",[ua,Do]=To(gn,{forceMount:void 0}),Lo=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,s=ce(gn,t);return m.jsx(ua,{scope:t,forceMount:n,children:c.Children.map(o,i=>m.jsx(ye,{present:n||s.open,children:m.jsx(St,{asChild:!0,container:r,children:i})}))})};Lo.displayName=gn;var ft="DialogOverlay",ko=c.forwardRef((e,t)=>{const n=Do(ft,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=ce(ft,e.__scopeDialog);return s.modal?m.jsx(ye,{present:o||s.open,children:m.jsx(fa,{...r,ref:t})}):null});ko.displayName=ft;var da=Ae("DialogOverlay.RemoveScroll"),fa=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ce(ft,n);return m.jsx(Rt,{as:da,allowPinchZoom:!0,shards:[r.contentRef],children:m.jsx(O.div,{"data-state":xn(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),Ie="DialogContent",Fo=c.forwardRef((e,t)=>{const n=Do(Ie,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=ce(Ie,e.__scopeDialog);return m.jsx(ye,{present:o||s.open,children:s.modal?m.jsx(pa,{...r,ref:t}):m.jsx(ma,{...r,ref:t})})});Fo.displayName=Ie;var pa=c.forwardRef((e,t)=>{const n=ce(Ie,e.__scopeDialog),o=c.useRef(null),r=H(t,n.contentRef,o);return c.useEffect(()=>{const s=o.current;if(s)return hn(s)},[]),m.jsx(jo,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:M(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:M(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:M(e.onFocusOutside,s=>s.preventDefault())})}),ma=c.forwardRef((e,t)=>{const n=ce(Ie,e.__scopeDialog),o=c.useRef(!1),r=c.useRef(!1);return m.jsx(jo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const i=s.target;n.triggerRef.current?.contains(i)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),jo=c.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,...i}=e,a=ce(Ie,n),l=c.useRef(null),u=H(t,l);return vn(),m.jsxs(m.Fragment,{children:[m.jsx(bt,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:m.jsx(Qe,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":xn(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(va,{titleId:a.titleId}),m.jsx(ga,{contentRef:l,descriptionId:a.descriptionId})]})]})}),wn="DialogTitle",$o=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ce(wn,n);return m.jsx(O.h2,{id:r.titleId,...o,ref:t})});$o.displayName=wn;var Bo="DialogDescription",Wo=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ce(Bo,n);return m.jsx(O.p,{id:r.descriptionId,...o,ref:t})});Wo.displayName=Bo;var Ho="DialogClose",Vo=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ce(Ho,n);return m.jsx(O.button,{type:"button",...o,ref:t,onClick:M(e.onClick,()=>r.onOpenChange(!1))})});Vo.displayName=Ho;function xn(e){return e?"open":"closed"}var Uo="DialogTitleWarning",[Nu,Ko]=Ws(Uo,{contentName:Ie,titleName:wn,docsSlug:"dialog"}),va=({titleId:e})=>{const t=Ko(Uo),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return c.useEffect(()=>{if(e){const o=document.getElementById(e)}},[n,e]),null},ha="DialogDescriptionWarning",ga=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ko(ha).contentName}}.`;return c.useEffect(()=>{const r=e.current?.getAttribute("aria-describedby");if(t&&r){const s=document.getElementById(t)}},[o,e,t]),null},Du=Oo,Lu=Lo,ku=ko,Fu=Fo,ju=$o,$u=Wo,Bu=Vo;const wa=["top","right","bottom","left"],Ce=Math.min,Q=Math.max,pt=Math.round,ct=Math.floor,de=e=>({x:e,y:e}),xa={left:"right",right:"left",bottom:"top",top:"bottom"},ya={start:"end",end:"start"};function en(e,t,n){return Q(e,Ce(t,n))}function ge(e,t){return typeof e=="function"?e(t):e}function we(e){return e.split("-")[0]}function He(e){return e.split("-")[1]}function yn(e){return e==="x"?"y":"x"}function Sn(e){return e==="y"?"height":"width"}function he(e){return["top","bottom"].includes(we(e))?"y":"x"}function Cn(e){return yn(he(e))}function Sa(e,t,n){n===void 0&&(n=!1);const o=He(e),r=Cn(e),s=Sn(r);let i=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=mt(i)),[i,mt(i)]}function Ca(e){const t=mt(e);return[tn(e),t,tn(t)]}function tn(e){return e.replace(/start|end/g,t=>ya[t])}function ba(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:i;default:return[]}}function Ea(e,t,n,o){const r=He(e);let s=ba(we(e),n==="start",o);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(tn)))),s}function mt(e){return e.replace(/left|right|bottom|top/g,t=>xa[t])}function Ra(e){return{top:0,right:0,bottom:0,left:0,...e}}function Go(e){return typeof e!="number"?Ra(e):{top:e,right:e,bottom:e,left:e}}function vt(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function Qn(e,t,n){let{reference:o,floating:r}=e;const s=he(t),i=Cn(t),a=Sn(i),l=we(t),u=s==="y",f=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,v=o[a]/2-r[a]/2;let h;switch(l){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:d};break;case"left":h={x:o.x-r.width,y:d};break;default:h={x:o.x,y:o.y}}switch(He(t)){case"start":h[i]-=v*(n&&u?-1:1);break;case"end":h[i]+=v*(n&&u?-1:1);break}return h}const Pa=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:d}=Qn(u,o,l),v=o,h={},w=0;for(let p=0;p<a.length;p++){const{name:g,fn:x}=a[p],{x:y,y:S,data:C,reset:b}=await x({x:f,y:d,initialPlacement:o,placement:v,strategy:r,middlewareData:h,rects:u,platform:i,elements:{reference:e,floating:t}});f=y??f,d=S??d,h={...h,[g]:{...h[g],...C}},b&&w<=50&&(w++,typeof b=="object"&&(b.placement&&(v=b.placement),b.rects&&(u=b.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):b.rects),{x:f,y:d}=Qn(u,v,l)),p=-1)}return{x:f,y:d,placement:v,strategy:r,middlewareData:h}};async function ze(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:v=!1,padding:h=0}=ge(t,e),w=Go(h),g=a[v?d==="floating"?"reference":"floating":d],x=vt(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:l})),y=d==="floating"?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,S=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),C=await(s.isElement==null?void 0:s.isElement(S))?await(s.getScale==null?void 0:s.getScale(S))||{x:1,y:1}:{x:1,y:1},b=vt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:S,strategy:l}):y);return{top:(x.top-b.top+w.top)/C.y,bottom:(b.bottom-x.bottom+w.bottom)/C.y,left:(x.left-b.left+w.left)/C.x,right:(b.right-x.right+w.right)/C.x}}const Ma=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:f=0}=ge(e,t)||{};if(u==null)return{};const d=Go(f),v={x:n,y:o},h=Cn(r),w=Sn(h),p=await i.getDimensions(u),g=h==="y",x=g?"top":"left",y=g?"bottom":"right",S=g?"clientHeight":"clientWidth",C=s.reference[w]+s.reference[h]-v[h]-s.floating[w],b=v[h]-s.reference[h],I=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let E=I?I[S]:0;(!E||!await(i.isElement==null?void 0:i.isElement(I)))&&(E=a.floating[S]||s.floating[w]);const A=C/2-b/2,D=E/2-p[w]/2-1,N=Ce(d[x],D),k=Ce(d[y],D),F=N,L=E-p[w]-k,j=E/2-p[w]/2+A,$=en(F,j,L),T=!l.arrow&&He(r)!=null&&j!==$&&s.reference[w]/2-(j<F?N:k)-p[w]/2<0,B=T?j<F?j-F:j-L:0;return{[h]:v[h]+B,data:{[h]:$,centerOffset:j-$-B,...T&&{alignmentOffset:B}},reset:T}}}),Aa=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:v,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:p=!0,...g}=ge(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=we(r),y=he(a),S=we(a)===a,C=await(l.isRTL==null?void 0:l.isRTL(u.floating)),b=v||(S||!p?[mt(a)]:Ca(a)),I=w!=="none";!v&&I&&b.push(...Ea(a,p,w,C));const E=[a,...b],A=await ze(t,g),D=[];let N=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&D.push(A[x]),d){const $=Sa(r,i,C);D.push(A[$[0]],A[$[1]])}if(N=[...N,{placement:r,overflows:D}],!D.every($=>$<=0)){var k,F;const $=(((k=s.flip)==null?void 0:k.index)||0)+1,T=E[$];if(T){var L;const _=d==="alignment"?y!==he(T):!1,R=((L=N[0])==null?void 0:L.overflows[0])>0;if(!_||R)return{data:{index:$,overflows:N},reset:{placement:T}}}let B=(F=N.filter(_=>_.overflows[0]<=0).sort((_,R)=>_.overflows[1]-R.overflows[1])[0])==null?void 0:F.placement;if(!B)switch(h){case"bestFit":{var j;const _=(j=N.filter(R=>{if(I){const W=he(R.placement);return W===y||W==="y"}return!0}).map(R=>[R.placement,R.overflows.filter(W=>W>0).reduce((W,Y)=>W+Y,0)]).sort((R,W)=>R[1]-W[1])[0])==null?void 0:j[0];_&&(B=_);break}case"initialPlacement":B=a;break}if(r!==B)return{reset:{placement:B}}}return{}}}};function Jn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eo(e){return wa.some(t=>e[t]>=0)}const Ia=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ge(e,t);switch(o){case"referenceHidden":{const s=await ze(t,{...r,elementContext:"reference"}),i=Jn(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:eo(i)}}}case"escaped":{const s=await ze(t,{...r,altBoundary:!0}),i=Jn(s,n.floating);return{data:{escapedOffsets:i,escaped:eo(i)}}}default:return{}}}}};async function _a(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),i=we(n),a=He(n),l=he(n)==="y",u=["left","top"].includes(i)?-1:1,f=s&&l?-1:1,d=ge(t,e);let{mainAxis:v,crossAxis:h,alignmentAxis:w}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof w=="number"&&(h=a==="end"?w*-1:w),l?{x:h*f,y:v*u}:{x:v*u,y:h*f}}const Ta=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:i,middlewareData:a}=t,l=await _a(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(o=a.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},Oa=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:g=>{let{x,y}=g;return{x,y}}},...l}=ge(e,t),u={x:n,y:o},f=await ze(t,l),d=he(we(r)),v=yn(d);let h=u[v],w=u[d];if(s){const g=v==="y"?"top":"left",x=v==="y"?"bottom":"right",y=h+f[g],S=h-f[x];h=en(y,h,S)}if(i){const g=d==="y"?"top":"left",x=d==="y"?"bottom":"right",y=w+f[g],S=w-f[x];w=en(y,w,S)}const p=a.fn({...t,[v]:h,[d]:w});return{...p,data:{x:p.x-n,y:p.y-o,enabled:{[v]:s,[d]:i}}}}}},Na=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=ge(e,t),f={x:n,y:o},d=he(r),v=yn(d);let h=f[v],w=f[d];const p=ge(a,t),g=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const S=v==="y"?"height":"width",C=s.reference[v]-s.floating[S]+g.mainAxis,b=s.reference[v]+s.reference[S]-g.mainAxis;h<C?h=C:h>b&&(h=b)}if(u){var x,y;const S=v==="y"?"width":"height",C=["top","left"].includes(we(r)),b=s.reference[d]-s.floating[S]+(C&&((x=i.offset)==null?void 0:x[d])||0)+(C?0:g.crossAxis),I=s.reference[d]+s.reference[S]+(C?0:((y=i.offset)==null?void 0:y[d])||0)-(C?g.crossAxis:0);w<b?w=b:w>I&&(w=I)}return{[v]:h,[d]:w}}}},Da=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=ge(e,t),f=await ze(t,u),d=we(r),v=He(r),h=he(r)==="y",{width:w,height:p}=s.floating;let g,x;d==="top"||d==="bottom"?(g=d,x=v===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=d,g=v==="end"?"top":"bottom");const y=p-f.top-f.bottom,S=w-f.left-f.right,C=Ce(p-f[g],y),b=Ce(w-f[x],S),I=!t.middlewareData.shift;let E=C,A=b;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(A=S),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(E=y),I&&!v){const N=Q(f.left,0),k=Q(f.right,0),F=Q(f.top,0),L=Q(f.bottom,0);h?A=w-2*(N!==0||k!==0?N+k:Q(f.left,f.right)):E=p-2*(F!==0||L!==0?F+L:Q(f.top,f.bottom))}await l({...t,availableWidth:A,availableHeight:E});const D=await i.getDimensions(a.floating);return w!==D.width||p!==D.height?{reset:{rects:!0}}:{}}}};function Mt(){return typeof window<"u"}function Ve(e){return zo(e)?(e.nodeName||"").toLowerCase():"#document"}function J(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function pe(e){var t;return(t=(zo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function zo(e){return Mt()?e instanceof Node||e instanceof J(e).Node:!1}function ie(e){return Mt()?e instanceof Element||e instanceof J(e).Element:!1}function fe(e){return Mt()?e instanceof HTMLElement||e instanceof J(e).HTMLElement:!1}function to(e){return!Mt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof J(e).ShadowRoot}function Je(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ae(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function La(e){return["table","td","th"].includes(Ve(e))}function At(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function bn(e){const t=En(),n=ie(e)?ae(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function ka(e){let t=be(e);for(;fe(t)&&!We(t);){if(bn(t))return t;if(At(t))return null;t=be(t)}return null}function En(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function We(e){return["html","body","#document"].includes(Ve(e))}function ae(e){return J(e).getComputedStyle(e)}function It(e){return ie(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function be(e){if(Ve(e)==="html")return e;const t=e.assignedSlot||e.parentNode||to(e)&&e.host||pe(e);return to(t)?t.host:t}function Yo(e){const t=be(e);return We(t)?e.ownerDocument?e.ownerDocument.body:e.body:fe(t)&&Je(t)?t:Yo(t)}function Ye(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Yo(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),i=J(r);if(s){const a=nn(i);return t.concat(i,i.visualViewport||[],Je(r)?r:[],a&&n?Ye(a):[])}return t.concat(r,Ye(r,[],n))}function nn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Xo(e){const t=ae(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=fe(e),s=r?e.offsetWidth:n,i=r?e.offsetHeight:o,a=pt(n)!==s||pt(o)!==i;return a&&(n=s,o=i),{width:n,height:o,$:a}}function Rn(e){return ie(e)?e:e.contextElement}function $e(e){const t=Rn(e);if(!fe(t))return de(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=Xo(t);let i=(s?pt(n.width):n.width)/o,a=(s?pt(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Fa=de(0);function qo(e){const t=J(e);return!En()||!t.visualViewport?Fa:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function ja(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==J(e)?!1:t}function _e(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=Rn(e);let i=de(1);t&&(o?ie(o)&&(i=$e(o)):i=$e(e));const a=ja(s,n,o)?qo(s):de(0);let l=(r.left+a.x)/i.x,u=(r.top+a.y)/i.y,f=r.width/i.x,d=r.height/i.y;if(s){const v=J(s),h=o&&ie(o)?J(o):o;let w=v,p=nn(w);for(;p&&o&&h!==w;){const g=$e(p),x=p.getBoundingClientRect(),y=ae(p),S=x.left+(p.clientLeft+parseFloat(y.paddingLeft))*g.x,C=x.top+(p.clientTop+parseFloat(y.paddingTop))*g.y;l*=g.x,u*=g.y,f*=g.x,d*=g.y,l+=S,u+=C,w=J(p),p=nn(w)}}return vt({width:f,height:d,x:l,y:u})}function Pn(e,t){const n=It(e).scrollLeft;return t?t.left+n:_e(pe(e)).left+n}function Zo(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:Pn(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function $a(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",i=pe(o),a=t?At(t.floating):!1;if(o===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},u=de(1);const f=de(0),d=fe(o);if((d||!d&&!s)&&((Ve(o)!=="body"||Je(i))&&(l=It(o)),fe(o))){const h=_e(o);u=$e(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const v=i&&!d&&!s?Zo(i,l,!0):de(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+f.x+v.x,y:n.y*u.y-l.scrollTop*u.y+f.y+v.y}}function Ba(e){return Array.from(e.getClientRects())}function Wa(e){const t=pe(e),n=It(e),o=e.ownerDocument.body,r=Q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=Q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let i=-n.scrollLeft+Pn(e);const a=-n.scrollTop;return ae(o).direction==="rtl"&&(i+=Q(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:i,y:a}}function Ha(e,t){const n=J(e),o=pe(e),r=n.visualViewport;let s=o.clientWidth,i=o.clientHeight,a=0,l=0;if(r){s=r.width,i=r.height;const u=En();(!u||u&&t==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:a,y:l}}function Va(e,t){const n=_e(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=fe(e)?$e(e):de(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=r*s.x,u=o*s.y;return{width:i,height:a,x:l,y:u}}function no(e,t,n){let o;if(t==="viewport")o=Ha(e,n);else if(t==="document")o=Wa(pe(e));else if(ie(t))o=Va(t,n);else{const r=qo(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return vt(o)}function Qo(e,t){const n=be(e);return n===t||!ie(n)||We(n)?!1:ae(n).position==="fixed"||Qo(n,t)}function Ua(e,t){const n=t.get(e);if(n)return n;let o=Ye(e,[],!1).filter(a=>ie(a)&&Ve(a)!=="body"),r=null;const s=ae(e).position==="fixed";let i=s?be(e):e;for(;ie(i)&&!We(i);){const a=ae(i),l=bn(i);!l&&a.position==="fixed"&&(r=null),(s?!l&&!r:!l&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Je(i)&&!l&&Qo(e,i))?o=o.filter(f=>f!==i):r=a,i=be(i)}return t.set(e,o),o}function Ka(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?At(t)?[]:Ua(t,this._c):[].concat(n),o],a=i[0],l=i.reduce((u,f)=>{const d=no(t,f,r);return u.top=Q(d.top,u.top),u.right=Ce(d.right,u.right),u.bottom=Ce(d.bottom,u.bottom),u.left=Q(d.left,u.left),u},no(t,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Ga(e){const{width:t,height:n}=Xo(e);return{width:t,height:n}}function za(e,t,n){const o=fe(t),r=pe(t),s=n==="fixed",i=_e(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=de(0);function u(){l.x=Pn(r)}if(o||!o&&!s)if((Ve(t)!=="body"||Je(r))&&(a=It(t)),o){const h=_e(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else r&&u();s&&!o&&r&&u();const f=r&&!o&&!s?Zo(r,a):de(0),d=i.left+a.scrollLeft-l.x-f.x,v=i.top+a.scrollTop-l.y-f.y;return{x:d,y:v,width:i.width,height:i.height}}function zt(e){return ae(e).position==="static"}function oo(e,t){if(!fe(e)||ae(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return pe(e)===n&&(n=n.ownerDocument.body),n}function Jo(e,t){const n=J(e);if(At(e))return n;if(!fe(e)){let r=be(e);for(;r&&!We(r);){if(ie(r)&&!zt(r))return r;r=be(r)}return n}let o=oo(e,t);for(;o&&La(o)&&zt(o);)o=oo(o,t);return o&&We(o)&&zt(o)&&!bn(o)?n:o||ka(e)||n}const Ya=async function(e){const t=this.getOffsetParent||Jo,n=this.getDimensions,o=await n(e.floating);return{reference:za(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Xa(e){return ae(e).direction==="rtl"}const qa={convertOffsetParentRelativeRectToViewportRelativeRect:$a,getDocumentElement:pe,getClippingRect:Ka,getOffsetParent:Jo,getElementRects:Ya,getClientRects:Ba,getDimensions:Ga,getScale:$e,isElement:ie,isRTL:Xa};function er(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Za(e,t){let n=null,o;const r=pe(e);function s(){var a;clearTimeout(o),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:f,top:d,width:v,height:h}=u;if(a||t(),!v||!h)return;const w=ct(d),p=ct(r.clientWidth-(f+v)),g=ct(r.clientHeight-(d+h)),x=ct(f),S={rootMargin:-w+"px "+-p+"px "+-g+"px "+-x+"px",threshold:Q(0,Ce(1,l))||1};let C=!0;function b(I){const E=I[0].intersectionRatio;if(E!==l){if(!C)return i();E?i(!1,E):o=setTimeout(()=>{i(!1,1e-7)},1e3)}E===1&&!er(u,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(b,{...S,root:r.ownerDocument})}catch{n=new IntersectionObserver(b,S)}n.observe(e)}return i(!0),s}function Qa(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,u=Rn(e),f=r||s?[...u?Ye(u):[],...Ye(t)]:[];f.forEach(x=>{r&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const d=u&&a?Za(u,n):null;let v=-1,h=null;i&&(h=new ResizeObserver(x=>{let[y]=x;y&&y.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var S;(S=h)==null||S.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let w,p=l?_e(e):null;l&&g();function g(){const x=_e(e);p&&!er(p,x)&&n(),p=x,w=requestAnimationFrame(g)}return n(),()=>{var x;f.forEach(y=>{r&&y.removeEventListener("scroll",n),s&&y.removeEventListener("resize",n)}),d?.(),(x=h)==null||x.disconnect(),h=null,l&&cancelAnimationFrame(w)}}const Ja=Ta,ec=Oa,tc=Aa,nc=Da,oc=Ia,ro=Ma,rc=Na,sc=(e,t,n)=>{const o=new Map,r={platform:qa,...n},s={...r.platform,_c:o};return Pa(e,t,{...r,platform:s})};var dt=typeof document<"u"?c.useLayoutEffect:c.useEffect;function ht(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!ht(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!ht(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function tr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function so(e,t){const n=tr(e);return Math.round(t*n)/n}function Yt(e){const t=c.useRef(e);return dt(()=>{t.current=e}),t}function ic(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[f,d]=c.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=c.useState(o);ht(v,o)||h(o);const[w,p]=c.useState(null),[g,x]=c.useState(null),y=c.useCallback(_=>{_!==I.current&&(I.current=_,p(_))},[]),S=c.useCallback(_=>{_!==E.current&&(E.current=_,x(_))},[]),C=s||w,b=i||g,I=c.useRef(null),E=c.useRef(null),A=c.useRef(f),D=l!=null,N=Yt(l),k=Yt(r),F=Yt(u),L=c.useCallback(()=>{if(!I.current||!E.current)return;const _={placement:t,strategy:n,middleware:v};k.current&&(_.platform=k.current),sc(I.current,E.current,_).then(R=>{const W={...R,isPositioned:F.current!==!1};j.current&&!ht(A.current,W)&&(A.current=W,Ze.flushSync(()=>{d(W)}))})},[v,t,n,k,F]);dt(()=>{u===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,d(_=>({..._,isPositioned:!1})))},[u]);const j=c.useRef(!1);dt(()=>(j.current=!0,()=>{j.current=!1}),[]),dt(()=>{if(C&&(I.current=C),b&&(E.current=b),C&&b){if(N.current)return N.current(C,b,L);L()}},[C,b,L,N,D]);const $=c.useMemo(()=>({reference:I,floating:E,setReference:y,setFloating:S}),[y,S]),T=c.useMemo(()=>({reference:C,floating:b}),[C,b]),B=c.useMemo(()=>{const _={position:n,left:0,top:0};if(!T.floating)return _;const R=so(T.floating,f.x),W=so(T.floating,f.y);return a?{..._,transform:"translate("+R+"px, "+W+"px)",...tr(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:W}},[n,a,T.floating,f.x,f.y]);return c.useMemo(()=>({...f,update:L,refs:$,elements:T,floatingStyles:B}),[f,L,$,T,B])}const ac=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?ro({element:o.current,padding:r}).fn(n):{}:o?ro({element:o,padding:r}).fn(n):{}}}},cc=(e,t)=>({...Ja(e),options:[e,t]}),lc=(e,t)=>({...ec(e),options:[e,t]}),uc=(e,t)=>({...rc(e),options:[e,t]}),dc=(e,t)=>({...tc(e),options:[e,t]}),fc=(e,t)=>({...nc(e),options:[e,t]}),pc=(e,t)=>({...oc(e),options:[e,t]}),mc=(e,t)=>({...ac(e),options:[e,t]});var vc="Arrow",nr=c.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return m.jsx(O.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});nr.displayName=vc;var hc=nr;function gc(e){const[t,n]=c.useState(void 0);return z(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var Mn="Popper",[or,_t]=xe(Mn),[wc,rr]=or(Mn),sr=e=>{const{__scopePopper:t,children:n}=e,[o,r]=c.useState(null);return m.jsx(wc,{scope:t,anchor:o,onAnchorChange:r,children:n})};sr.displayName=Mn;var ir="PopperAnchor",ar=c.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=rr(ir,n),i=c.useRef(null),a=H(t,i);return c.useEffect(()=>{s.onAnchorChange(o?.current||i.current)}),o?null:m.jsx(O.div,{...r,ref:a})});ar.displayName=ir;var An="PopperContent",[xc,yc]=or(An),cr=c.forwardRef((e,t)=>{const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:v=!1,updatePositionStrategy:h="optimized",onPlaced:w,...p}=e,g=rr(An,n),[x,y]=c.useState(null),S=H(t,P=>y(P)),[C,b]=c.useState(null),I=gc(C),E=I?.width??0,A=I?.height??0,D=o+(s!=="center"?"-"+s:""),N=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},k=Array.isArray(u)?u:[u],F=k.length>0,L={padding:N,boundary:k.filter(Cc),altBoundary:F},{refs:j,floatingStyles:$,placement:T,isPositioned:B,middlewareData:_}=ic({strategy:"fixed",placement:D,whileElementsMounted:(...P)=>Qa(...P,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[cc({mainAxis:r+A,alignmentAxis:i}),l&&lc({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?uc():void 0,...L}),l&&dc({...L}),fc({...L,apply:({elements:P,rects:V,availableWidth:X,availableHeight:U})=>{const{width:K,height:G}=V.reference,te=P.floating.style;te.setProperty("--radix-popper-available-width",`${X}px`),te.setProperty("--radix-popper-available-height",`${U}px`),te.setProperty("--radix-popper-anchor-width",`${K}px`),te.setProperty("--radix-popper-anchor-height",`${G}px`)}}),C&&mc({element:C,padding:a}),bc({arrowWidth:E,arrowHeight:A}),v&&pc({strategy:"referenceHidden",...L})]}),[R,W]=dr(T),Y=se(w);z(()=>{B&&Y?.()},[B,Y]);const oe=_.arrow?.x,me=_.arrow?.y,ee=_.arrow?.centerOffset!==0,[ve,Z]=c.useState();return z(()=>{x&&Z(window.getComputedStyle(x).zIndex)},[x]),m.jsx("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:B?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ve,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:m.jsx(xc,{scope:n,placedSide:R,onArrowChange:b,arrowX:oe,arrowY:me,shouldHideArrow:ee,children:m.jsx(O.div,{"data-side":R,"data-align":W,...p,ref:S,style:{...p.style,animation:B?void 0:"none"}})})})});cr.displayName=An;var lr="PopperArrow",Sc={top:"bottom",right:"left",bottom:"top",left:"right"},ur=c.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=yc(lr,o),i=Sc[s.placedSide];return m.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:m.jsx(hc,{...r,ref:n,style:{...r.style,display:"block"}})})});ur.displayName=lr;function Cc(e){return e!==null}var bc=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:o,middlewareData:r}=t,i=r.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,f]=dr(n),d={start:"0%",center:"50%",end:"100%"}[f],v=(r.arrow?.x??0)+a/2,h=(r.arrow?.y??0)+l/2;let w="",p="";return u==="bottom"?(w=i?d:`${v}px`,p=`${-l}px`):u==="top"?(w=i?d:`${v}px`,p=`${o.floating.height+l}px`):u==="right"?(w=`${-l}px`,p=i?d:`${h}px`):u==="left"&&(w=`${o.floating.width+l}px`,p=i?d:`${h}px`),{data:{x:w,y:p}}}});function dr(e){const[t,n="center"]=e.split("-");return[t,n]}var fr=sr,pr=ar,mr=cr,vr=ur,Xt={exports:{}},qt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var io;function Ec(){if(io)return qt;io=1;var e=$s();function t(d,v){return d===v&&(d!==0||1/d===1/v)||d!==d&&v!==v}var n=typeof Object.is=="function"?Object.is:t,o=e.useState,r=e.useEffect,s=e.useLayoutEffect,i=e.useDebugValue;function a(d,v){var h=v(),w=o({inst:{value:h,getSnapshot:v}}),p=w[0].inst,g=w[1];return s(function(){p.value=h,p.getSnapshot=v,l(p)&&g({inst:p})},[d,h,v]),r(function(){return l(p)&&g({inst:p}),d(function(){l(p)&&g({inst:p})})},[d]),i(h),h}function l(d){var v=d.getSnapshot;d=d.value;try{var h=v();return!n(d,h)}catch{return!0}}function u(d,v){return v()}var f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u:a;return qt.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:f,qt}var ao;function Rc(){return ao||(ao=1,Xt.exports=Ec()),Xt.exports}var Pc=Rc();function Mc(){return Pc.useSyncExternalStore(Ac,()=>!0,()=>!1)}function Ac(){return()=>{}}var In="Avatar",[Ic,Wu]=xe(In),[_c,hr]=Ic(In),gr=c.forwardRef((e,t)=>{const{__scopeAvatar:n,...o}=e,[r,s]=c.useState("idle");return m.jsx(_c,{scope:n,imageLoadingStatus:r,onImageLoadingStatusChange:s,children:m.jsx(O.span,{...o,ref:t})})});gr.displayName=In;var wr="AvatarImage",Tc=c.forwardRef((e,t)=>{const{__scopeAvatar:n,src:o,onLoadingStatusChange:r=()=>{},...s}=e,i=hr(wr,n),a=Oc(o,s),l=se(u=>{r(u),i.onImageLoadingStatusChange(u)});return z(()=>{a!=="idle"&&l(a)},[a,l]),a==="loaded"?m.jsx(O.img,{...s,ref:t,src:o}):null});Tc.displayName=wr;var xr="AvatarFallback",yr=c.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:o,...r}=e,s=hr(xr,n),[i,a]=c.useState(o===void 0);return c.useEffect(()=>{if(o!==void 0){const l=window.setTimeout(()=>a(!0),o);return()=>window.clearTimeout(l)}},[o]),i&&s.imageLoadingStatus!=="loaded"?m.jsx(O.span,{...r,ref:t}):null});yr.displayName=xr;function co(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Oc(e,{referrerPolicy:t,crossOrigin:n}){const o=Mc(),r=c.useRef(null),s=o?(r.current||(r.current=new window.Image),r.current):null,[i,a]=c.useState(()=>co(s,e));return z(()=>{a(co(s,e))},[s,e]),z(()=>{const l=d=>()=>{a(d)};if(!s)return;const u=l("loaded"),f=l("error");return s.addEventListener("load",u),s.addEventListener("error",f),t&&(s.referrerPolicy=t),typeof n=="string"&&(s.crossOrigin=n),()=>{s.removeEventListener("load",u),s.removeEventListener("error",f)}},[s,n,t]),i}var Hu=gr,Vu=yr,Nc=c.createContext(void 0);function _n(e){const t=c.useContext(Nc);return e||t||"ltr"}var Zt="rovingFocusGroup.onEntryFocus",Dc={bubbles:!1,cancelable:!0},et="RovingFocusGroup",[on,Sr,Lc]=dn(et),[kc,Cr]=xe(et,[Lc]),[Fc,jc]=kc(et),br=c.forwardRef((e,t)=>m.jsx(on.Provider,{scope:e.__scopeRovingFocusGroup,children:m.jsx(on.Slot,{scope:e.__scopeRovingFocusGroup,children:m.jsx($c,{...e,ref:t})})}));br.displayName=et;var $c=c.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...d}=e,v=c.useRef(null),h=H(t,v),w=_n(s),[p,g]=Be({prop:i,defaultProp:a??null,onChange:l,caller:et}),[x,y]=c.useState(!1),S=se(u),C=Sr(n),b=c.useRef(!1),[I,E]=c.useState(0);return c.useEffect(()=>{const A=v.current;if(A)return A.addEventListener(Zt,S),()=>A.removeEventListener(Zt,S)},[S]),m.jsx(Fc,{scope:n,orientation:o,dir:w,loop:r,currentTabStopId:p,onItemFocus:c.useCallback(A=>g(A),[g]),onItemShiftTab:c.useCallback(()=>y(!0),[]),onFocusableItemAdd:c.useCallback(()=>E(A=>A+1),[]),onFocusableItemRemove:c.useCallback(()=>E(A=>A-1),[]),children:m.jsx(O.div,{tabIndex:x||I===0?-1:0,"data-orientation":o,...d,ref:h,style:{outline:"none",...e.style},onMouseDown:M(e.onMouseDown,()=>{b.current=!0}),onFocus:M(e.onFocus,A=>{const D=!b.current;if(A.target===A.currentTarget&&D&&!x){const N=new CustomEvent(Zt,Dc);if(A.currentTarget.dispatchEvent(N),!N.defaultPrevented){const k=C().filter(T=>T.focusable),F=k.find(T=>T.active),L=k.find(T=>T.id===p),$=[F,L,...k].filter(Boolean).map(T=>T.ref.current);Pr($,f)}}b.current=!1}),onBlur:M(e.onBlur,()=>y(!1))})})}),Er="RovingFocusGroupItem",Rr=c.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:s,children:i,...a}=e,l=ue(),u=s||l,f=jc(Er,n),d=f.currentTabStopId===u,v=Sr(n),{onFocusableItemAdd:h,onFocusableItemRemove:w,currentTabStopId:p}=f;return c.useEffect(()=>{if(o)return h(),()=>w()},[o,h,w]),m.jsx(on.ItemSlot,{scope:n,id:u,focusable:o,active:r,children:m.jsx(O.span,{tabIndex:d?0:-1,"data-orientation":f.orientation,...a,ref:t,onMouseDown:M(e.onMouseDown,g=>{o?f.onItemFocus(u):g.preventDefault()}),onFocus:M(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:M(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){f.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const x=Hc(g,f.orientation,f.dir);if(x!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let S=v().filter(C=>C.focusable).map(C=>C.ref.current);if(x==="last")S.reverse();else if(x==="prev"||x==="next"){x==="prev"&&S.reverse();const C=S.indexOf(g.currentTarget);S=f.loop?Vc(S,C+1):S.slice(C+1)}setTimeout(()=>Pr(S))}}),children:typeof i=="function"?i({isCurrentTabStop:d,hasTabStop:p!=null}):i})})});Rr.displayName=Er;var Bc={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Wc(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Hc(e,t,n){const o=Wc(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return Bc[o]}function Pr(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function Vc(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Uc=br,Kc=Rr,rn=["Enter"," "],Gc=["ArrowDown","PageUp","Home"],Mr=["ArrowUp","PageDown","End"],zc=[...Gc,...Mr],Yc={ltr:[...rn,"ArrowRight"],rtl:[...rn,"ArrowLeft"]},Xc={ltr:["ArrowLeft"],rtl:["ArrowRight"]},tt="Menu",[Xe,qc,Zc]=dn(tt),[Ne,Ar]=xe(tt,[Zc,_t,Cr]),Tt=_t(),Ir=Cr(),[Qc,De]=Ne(tt),[Jc,nt]=Ne(tt),_r=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:r,onOpenChange:s,modal:i=!0}=e,a=Tt(t),[l,u]=c.useState(null),f=c.useRef(!1),d=se(s),v=_n(r);return c.useEffect(()=>{const h=()=>{f.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>f.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),m.jsx(fr,{...a,children:m.jsx(Qc,{scope:t,open:n,onOpenChange:d,content:l,onContentChange:u,children:m.jsx(Jc,{scope:t,onClose:c.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:v,modal:i,children:o})})})};_r.displayName=tt;var el="MenuAnchor",Tn=c.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Tt(n);return m.jsx(pr,{...r,...o,ref:t})});Tn.displayName=el;var On="MenuPortal",[tl,Tr]=Ne(On,{forceMount:void 0}),Or=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:r}=e,s=De(On,t);return m.jsx(tl,{scope:t,forceMount:n,children:m.jsx(ye,{present:n||s.open,children:m.jsx(St,{asChild:!0,container:r,children:o})})})};Or.displayName=On;var ne="MenuContent",[nl,Nn]=Ne(ne),Nr=c.forwardRef((e,t)=>{const n=Tr(ne,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=De(ne,e.__scopeMenu),i=nt(ne,e.__scopeMenu);return m.jsx(Xe.Provider,{scope:e.__scopeMenu,children:m.jsx(ye,{present:o||s.open,children:m.jsx(Xe.Slot,{scope:e.__scopeMenu,children:i.modal?m.jsx(ol,{...r,ref:t}):m.jsx(rl,{...r,ref:t})})})})}),ol=c.forwardRef((e,t)=>{const n=De(ne,e.__scopeMenu),o=c.useRef(null),r=H(t,o);return c.useEffect(()=>{const s=o.current;if(s)return hn(s)},[]),m.jsx(Dn,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:M(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),rl=c.forwardRef((e,t)=>{const n=De(ne,e.__scopeMenu);return m.jsx(Dn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),sl=Ae("MenuContent.ScrollLock"),Dn=c.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,onDismiss:h,disableOutsideScroll:w,...p}=e,g=De(ne,n),x=nt(ne,n),y=Tt(n),S=Ir(n),C=qc(n),[b,I]=c.useState(null),E=c.useRef(null),A=H(t,E,g.onContentChange),D=c.useRef(0),N=c.useRef(""),k=c.useRef(0),F=c.useRef(null),L=c.useRef("right"),j=c.useRef(0),$=w?Rt:c.Fragment,T=w?{as:sl,allowPinchZoom:!0}:void 0,B=R=>{const W=N.current+R,Y=C().filter(P=>!P.disabled),oe=document.activeElement,me=Y.find(P=>P.ref.current===oe)?.textValue,ee=Y.map(P=>P.textValue),ve=gl(ee,W,me),Z=Y.find(P=>P.textValue===ve)?.ref.current;(function P(V){N.current=V,window.clearTimeout(D.current),V!==""&&(D.current=window.setTimeout(()=>P(""),1e3))})(W),Z&&setTimeout(()=>Z.focus())};c.useEffect(()=>()=>window.clearTimeout(D.current),[]),vn();const _=c.useCallback(R=>L.current===F.current?.side&&xl(R,F.current?.area),[]);return m.jsx(nl,{scope:n,searchRef:N,onItemEnter:c.useCallback(R=>{_(R)&&R.preventDefault()},[_]),onItemLeave:c.useCallback(R=>{_(R)||(E.current?.focus(),I(null))},[_]),onTriggerLeave:c.useCallback(R=>{_(R)&&R.preventDefault()},[_]),pointerGraceTimerRef:k,onPointerGraceIntentChange:c.useCallback(R=>{F.current=R},[]),children:m.jsx($,{...T,children:m.jsx(bt,{asChild:!0,trapped:r,onMountAutoFocus:M(s,R=>{R.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:m.jsx(Qe,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,onDismiss:h,children:m.jsx(Uc,{asChild:!0,...S,dir:x.dir,orientation:"vertical",loop:o,currentTabStopId:b,onCurrentTabStopIdChange:I,onEntryFocus:M(l,R=>{x.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:m.jsx(mr,{role:"menu","aria-orientation":"vertical","data-state":Xr(g.open),"data-radix-menu-content":"",dir:x.dir,...y,...p,ref:A,style:{outline:"none",...p.style},onKeyDown:M(p.onKeyDown,R=>{const Y=R.target.closest("[data-radix-menu-content]")===R.currentTarget,oe=R.ctrlKey||R.altKey||R.metaKey,me=R.key.length===1;Y&&(R.key==="Tab"&&R.preventDefault(),!oe&&me&&B(R.key));const ee=E.current;if(R.target!==ee||!zc.includes(R.key))return;R.preventDefault();const Z=C().filter(P=>!P.disabled).map(P=>P.ref.current);Mr.includes(R.key)&&Z.reverse(),vl(Z)}),onBlur:M(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(D.current),N.current="")}),onPointerMove:M(e.onPointerMove,qe(R=>{const W=R.target,Y=j.current!==R.clientX;if(R.currentTarget.contains(W)&&Y){const oe=R.clientX>j.current?"right":"left";L.current=oe,j.current=R.clientX}}))})})})})})})});Nr.displayName=ne;var il="MenuGroup",Ln=c.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(O.div,{role:"group",...o,ref:t})});Ln.displayName=il;var al="MenuLabel",Dr=c.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(O.div,{...o,ref:t})});Dr.displayName=al;var gt="MenuItem",lo="menu.itemSelect",Ot=c.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...r}=e,s=c.useRef(null),i=nt(gt,e.__scopeMenu),a=Nn(gt,e.__scopeMenu),l=H(t,s),u=c.useRef(!1),f=()=>{const d=s.current;if(!n&&d){const v=new CustomEvent(lo,{bubbles:!0,cancelable:!0});d.addEventListener(lo,h=>o?.(h),{once:!0}),mo(d,v),v.defaultPrevented?u.current=!1:i.onClose()}};return m.jsx(Lr,{...r,ref:l,disabled:n,onClick:M(e.onClick,f),onPointerDown:d=>{e.onPointerDown?.(d),u.current=!0},onPointerUp:M(e.onPointerUp,d=>{u.current||d.currentTarget?.click()}),onKeyDown:M(e.onKeyDown,d=>{const v=a.searchRef.current!=="";n||v&&d.key===" "||rn.includes(d.key)&&(d.currentTarget.click(),d.preventDefault())})})});Ot.displayName=gt;var Lr=c.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:r,...s}=e,i=Nn(gt,n),a=Ir(n),l=c.useRef(null),u=H(t,l),[f,d]=c.useState(!1),[v,h]=c.useState("");return c.useEffect(()=>{const w=l.current;w&&h((w.textContent??"").trim())},[s.children]),m.jsx(Xe.ItemSlot,{scope:n,disabled:o,textValue:r??v,children:m.jsx(Kc,{asChild:!0,...a,focusable:!o,children:m.jsx(O.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:u,onPointerMove:M(e.onPointerMove,qe(w=>{o?i.onItemLeave(w):(i.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:M(e.onPointerLeave,qe(w=>i.onItemLeave(w))),onFocus:M(e.onFocus,()=>d(!0)),onBlur:M(e.onBlur,()=>d(!1))})})})}),cl="MenuCheckboxItem",kr=c.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...r}=e;return m.jsx(Wr,{scope:e.__scopeMenu,checked:n,children:m.jsx(Ot,{role:"menuitemcheckbox","aria-checked":wt(n)?"mixed":n,...r,ref:t,"data-state":Fn(n),onSelect:M(r.onSelect,()=>o?.(wt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});kr.displayName=cl;var Fr="MenuRadioGroup",[ll,ul]=Ne(Fr,{value:void 0,onValueChange:()=>{}}),jr=c.forwardRef((e,t)=>{const{value:n,onValueChange:o,...r}=e,s=se(o);return m.jsx(ll,{scope:e.__scopeMenu,value:n,onValueChange:s,children:m.jsx(Ln,{...r,ref:t})})});jr.displayName=Fr;var $r="MenuRadioItem",Br=c.forwardRef((e,t)=>{const{value:n,...o}=e,r=ul($r,e.__scopeMenu),s=n===r.value;return m.jsx(Wr,{scope:e.__scopeMenu,checked:s,children:m.jsx(Ot,{role:"menuitemradio","aria-checked":s,...o,ref:t,"data-state":Fn(s),onSelect:M(o.onSelect,()=>r.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});Br.displayName=$r;var kn="MenuItemIndicator",[Wr,dl]=Ne(kn,{checked:!1}),Hr=c.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...r}=e,s=dl(kn,n);return m.jsx(ye,{present:o||wt(s.checked)||s.checked===!0,children:m.jsx(O.span,{...r,ref:t,"data-state":Fn(s.checked)})})});Hr.displayName=kn;var fl="MenuSeparator",Vr=c.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(O.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});Vr.displayName=fl;var pl="MenuArrow",Ur=c.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Tt(n);return m.jsx(vr,{...r,...o,ref:t})});Ur.displayName=pl;var ml="MenuSub",[Uu,Kr]=Ne(ml),Ke="MenuSubTrigger",Gr=c.forwardRef((e,t)=>{const n=De(Ke,e.__scopeMenu),o=nt(Ke,e.__scopeMenu),r=Kr(Ke,e.__scopeMenu),s=Nn(Ke,e.__scopeMenu),i=c.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},f=c.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return c.useEffect(()=>f,[f]),c.useEffect(()=>{const d=a.current;return()=>{window.clearTimeout(d),l(null)}},[a,l]),m.jsx(Tn,{asChild:!0,...u,children:m.jsx(Lr,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":Xr(n.open),...e,ref:yt(t,r.onTriggerChange),onClick:d=>{e.onClick?.(d),!(e.disabled||d.defaultPrevented)&&(d.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:M(e.onPointerMove,qe(d=>{s.onItemEnter(d),!d.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(s.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:M(e.onPointerLeave,qe(d=>{f();const v=n.content?.getBoundingClientRect();if(v){const h=n.content?.dataset.side,w=h==="right",p=w?-5:5,g=v[w?"left":"right"],x=v[w?"right":"left"];s.onPointerGraceIntentChange({area:[{x:d.clientX+p,y:d.clientY},{x:g,y:v.top},{x,y:v.top},{x,y:v.bottom},{x:g,y:v.bottom}],side:h}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(d),d.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:M(e.onKeyDown,d=>{const v=s.searchRef.current!=="";e.disabled||v&&d.key===" "||Yc[o.dir].includes(d.key)&&(n.onOpenChange(!0),n.content?.focus(),d.preventDefault())})})})});Gr.displayName=Ke;var zr="MenuSubContent",Yr=c.forwardRef((e,t)=>{const n=Tr(ne,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=De(ne,e.__scopeMenu),i=nt(ne,e.__scopeMenu),a=Kr(zr,e.__scopeMenu),l=c.useRef(null),u=H(t,l);return m.jsx(Xe.Provider,{scope:e.__scopeMenu,children:m.jsx(ye,{present:o||s.open,children:m.jsx(Xe.Slot,{scope:e.__scopeMenu,children:m.jsx(Dn,{id:a.contentId,"aria-labelledby":a.triggerId,...r,ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{i.isUsingKeyboardRef.current&&l.current?.focus(),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:M(e.onFocusOutside,f=>{f.target!==a.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:M(e.onEscapeKeyDown,f=>{i.onClose(),f.preventDefault()}),onKeyDown:M(e.onKeyDown,f=>{const d=f.currentTarget.contains(f.target),v=Xc[i.dir].includes(f.key);d&&v&&(s.onOpenChange(!1),a.trigger?.focus(),f.preventDefault())})})})})})});Yr.displayName=zr;function Xr(e){return e?"open":"closed"}function wt(e){return e==="indeterminate"}function Fn(e){return wt(e)?"indeterminate":e?"checked":"unchecked"}function vl(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function hl(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function gl(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=hl(e,Math.max(s,0));r.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function wl(e,t){const{x:n,y:o}=e;let r=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s],l=t[i],u=a.x,f=a.y,d=l.x,v=l.y;f>o!=v>o&&n<(d-u)*(o-f)/(v-f)+u&&(r=!r)}return r}function xl(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return wl(n,t)}function qe(e){return t=>t.pointerType==="mouse"?e(t):void 0}var yl=_r,Sl=Tn,Cl=Or,bl=Nr,El=Ln,Rl=Dr,Pl=Ot,Ml=kr,Al=jr,Il=Br,_l=Hr,Tl=Vr,Ol=Ur,Nl=Gr,Dl=Yr,Nt="DropdownMenu",[Ll,Ku]=xe(Nt,[Ar]),q=Ar(),[kl,qr]=Ll(Nt),Zr=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:r,defaultOpen:s,onOpenChange:i,modal:a=!0}=e,l=q(t),u=c.useRef(null),[f,d]=Be({prop:r,defaultProp:s??!1,onChange:i,caller:Nt});return m.jsx(kl,{scope:t,triggerId:ue(),triggerRef:u,contentId:ue(),open:f,onOpenChange:d,onOpenToggle:c.useCallback(()=>d(v=>!v),[d]),modal:a,children:m.jsx(yl,{...l,open:f,onOpenChange:d,dir:o,modal:a,children:n})})};Zr.displayName=Nt;var Qr="DropdownMenuTrigger",Jr=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...r}=e,s=qr(Qr,n),i=q(n);return m.jsx(Sl,{asChild:!0,...i,children:m.jsx(O.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:yt(t,s.triggerRef),onPointerDown:M(e.onPointerDown,a=>{!o&&a.button===0&&a.ctrlKey===!1&&(s.onOpenToggle(),s.open||a.preventDefault())}),onKeyDown:M(e.onKeyDown,a=>{o||(["Enter"," "].includes(a.key)&&s.onOpenToggle(),a.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});Jr.displayName=Qr;var Fl="DropdownMenuPortal",es=e=>{const{__scopeDropdownMenu:t,...n}=e,o=q(t);return m.jsx(Cl,{...o,...n})};es.displayName=Fl;var ts="DropdownMenuContent",ns=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=qr(ts,n),s=q(n),i=c.useRef(!1);return m.jsx(bl,{id:r.contentId,"aria-labelledby":r.triggerId,...s,...o,ref:t,onCloseAutoFocus:M(e.onCloseAutoFocus,a=>{i.current||r.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:M(e.onInteractOutside,a=>{const l=a.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,f=l.button===2||u;(!r.modal||f)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ns.displayName=ts;var jl="DropdownMenuGroup",os=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(El,{...r,...o,ref:t})});os.displayName=jl;var $l="DropdownMenuLabel",rs=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Rl,{...r,...o,ref:t})});rs.displayName=$l;var Bl="DropdownMenuItem",ss=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Pl,{...r,...o,ref:t})});ss.displayName=Bl;var Wl="DropdownMenuCheckboxItem",Hl=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Ml,{...r,...o,ref:t})});Hl.displayName=Wl;var Vl="DropdownMenuRadioGroup",Ul=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Al,{...r,...o,ref:t})});Ul.displayName=Vl;var Kl="DropdownMenuRadioItem",Gl=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Il,{...r,...o,ref:t})});Gl.displayName=Kl;var zl="DropdownMenuItemIndicator",Yl=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(_l,{...r,...o,ref:t})});Yl.displayName=zl;var Xl="DropdownMenuSeparator",is=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Tl,{...r,...o,ref:t})});is.displayName=Xl;var ql="DropdownMenuArrow",Zl=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Ol,{...r,...o,ref:t})});Zl.displayName=ql;var Ql="DropdownMenuSubTrigger",Jl=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Nl,{...r,...o,ref:t})});Jl.displayName=Ql;var eu="DropdownMenuSubContent",tu=c.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Dl,{...r,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tu.displayName=eu;var Gu=Zr,zu=Jr,Yu=es,Xu=ns,qu=os,Zu=rs,Qu=ss,Ju=is,nu="Label",as=c.forwardRef((e,t)=>m.jsx(O.label,{...e,ref:t,onMouseDown:n=>{n.target.closest("button, input, select, textarea")||(e.onMouseDown?.(n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));as.displayName=nu;var ed=as;function uo(e,[t,n]){return Math.min(n,Math.max(t,e))}function ou(e){const t=c.useRef({value:e,previous:e});return c.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var ru=[" ","Enter","ArrowUp","ArrowDown"],su=[" ","Enter"],Te="Select",[Dt,Lt,iu]=dn(Te),[Ue,td]=xe(Te,[iu,_t]),kt=_t(),[au,Ee]=Ue(Te),[cu,lu]=Ue(Te),cs=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:i,defaultValue:a,onValueChange:l,dir:u,name:f,autoComplete:d,disabled:v,required:h,form:w}=e,p=kt(t),[g,x]=c.useState(null),[y,S]=c.useState(null),[C,b]=c.useState(!1),I=_n(u),[E,A]=Be({prop:o,defaultProp:r??!1,onChange:s,caller:Te}),[D,N]=Be({prop:i,defaultProp:a,onChange:l,caller:Te}),k=c.useRef(null),F=g?w||!!g.closest("form"):!0,[L,j]=c.useState(new Set),$=Array.from(L).map(T=>T.props.value).join(";");return m.jsx(fr,{...p,children:m.jsxs(au,{required:h,scope:t,trigger:g,onTriggerChange:x,valueNode:y,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:b,contentId:ue(),value:D,onValueChange:N,open:E,onOpenChange:A,dir:I,triggerPointerDownPosRef:k,disabled:v,children:[m.jsx(Dt.Provider,{scope:t,children:m.jsx(cu,{scope:e.__scopeSelect,onNativeOptionAdd:c.useCallback(T=>{j(B=>new Set(B).add(T))},[]),onNativeOptionRemove:c.useCallback(T=>{j(B=>{const _=new Set(B);return _.delete(T),_})},[]),children:n})}),F?m.jsxs(Ns,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:d,value:D,onChange:T=>N(T.target.value),disabled:v,form:w,children:[D===void 0?m.jsx("option",{value:""}):null,Array.from(L)]},$):null]})})};cs.displayName=Te;var ls="SelectTrigger",us=c.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=kt(n),i=Ee(ls,n),a=i.disabled||o,l=H(t,i.onTriggerChange),u=Lt(n),f=c.useRef("touch"),[d,v,h]=Ls(p=>{const g=u().filter(S=>!S.disabled),x=g.find(S=>S.value===i.value),y=ks(g,p,x);y!==void 0&&i.onValueChange(y.value)}),w=p=>{a||(i.onOpenChange(!0),h()),p&&(i.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return m.jsx(pr,{asChild:!0,...s,children:m.jsx(O.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":Ds(i.value)?"":void 0,...r,ref:l,onClick:M(r.onClick,p=>{p.currentTarget.focus(),f.current!=="mouse"&&w(p)}),onPointerDown:M(r.onPointerDown,p=>{f.current=p.pointerType;const g=p.target;g.hasPointerCapture(p.pointerId)&&g.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(w(p),p.preventDefault())}),onKeyDown:M(r.onKeyDown,p=>{const g=d.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&v(p.key),!(g&&p.key===" ")&&ru.includes(p.key)&&(w(),p.preventDefault())})})})});us.displayName=ls;var ds="SelectValue",fs=c.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:i="",...a}=e,l=Ee(ds,n),{onValueNodeHasChildrenChange:u}=l,f=s!==void 0,d=H(t,l.onValueNodeChange);return z(()=>{u(f)},[u,f]),m.jsx(O.span,{...a,ref:d,style:{pointerEvents:"none"},children:Ds(l.value)?m.jsx(m.Fragment,{children:i}):s})});fs.displayName=ds;var uu="SelectIcon",ps=c.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return m.jsx(O.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});ps.displayName=uu;var du="SelectPortal",ms=e=>m.jsx(St,{asChild:!0,...e});ms.displayName=du;var Oe="SelectContent",vs=c.forwardRef((e,t)=>{const n=Ee(Oe,e.__scopeSelect),[o,r]=c.useState();if(z(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?Ze.createPortal(m.jsx(hs,{scope:e.__scopeSelect,children:m.jsx(Dt.Slot,{scope:e.__scopeSelect,children:m.jsx("div",{children:e.children})})}),s):null}return m.jsx(gs,{...e,ref:t})});vs.displayName=Oe;var re=10,[hs,Re]=Ue(Oe),fu="SelectContentImpl",pu=Ae("SelectContent.RemoveScroll"),gs=c.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:i,side:a,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:v,collisionPadding:h,sticky:w,hideWhenDetached:p,avoidCollisions:g,...x}=e,y=Ee(Oe,n),[S,C]=c.useState(null),[b,I]=c.useState(null),E=H(t,P=>C(P)),[A,D]=c.useState(null),[N,k]=c.useState(null),F=Lt(n),[L,j]=c.useState(!1),$=c.useRef(!1);c.useEffect(()=>{if(S)return hn(S)},[S]),vn();const T=c.useCallback(P=>{const[V,...X]=F().map(G=>G.ref.current),[U]=X.slice(-1),K=document.activeElement;for(const G of P)if(G===K||(G?.scrollIntoView({block:"nearest"}),G===V&&b&&(b.scrollTop=0),G===U&&b&&(b.scrollTop=b.scrollHeight),G?.focus(),document.activeElement!==K))return},[F,b]),B=c.useCallback(()=>T([A,S]),[T,A,S]);c.useEffect(()=>{L&&B()},[L,B]);const{onOpenChange:_,triggerPointerDownPosRef:R}=y;c.useEffect(()=>{if(S){let P={x:0,y:0};const V=U=>{P={x:Math.abs(Math.round(U.pageX)-(R.current?.x??0)),y:Math.abs(Math.round(U.pageY)-(R.current?.y??0))}},X=U=>{P.x<=10&&P.y<=10?U.preventDefault():S.contains(U.target)||_(!1),document.removeEventListener("pointermove",V),R.current=null};return R.current!==null&&(document.addEventListener("pointermove",V),document.addEventListener("pointerup",X,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",V),document.removeEventListener("pointerup",X,{capture:!0})}}},[S,_,R]),c.useEffect(()=>{const P=()=>_(!1);return window.addEventListener("blur",P),window.addEventListener("resize",P),()=>{window.removeEventListener("blur",P),window.removeEventListener("resize",P)}},[_]);const[W,Y]=Ls(P=>{const V=F().filter(K=>!K.disabled),X=V.find(K=>K.ref.current===document.activeElement),U=ks(V,P,X);U&&setTimeout(()=>U.ref.current.focus())}),oe=c.useCallback((P,V,X)=>{const U=!$.current&&!X;(y.value!==void 0&&y.value===V||U)&&(D(P),U&&($.current=!0))},[y.value]),me=c.useCallback(()=>S?.focus(),[S]),ee=c.useCallback((P,V,X)=>{const U=!$.current&&!X;(y.value!==void 0&&y.value===V||U)&&k(P)},[y.value]),ve=o==="popper"?sn:ws,Z=ve===sn?{side:a,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:v,collisionPadding:h,sticky:w,hideWhenDetached:p,avoidCollisions:g}:{};return m.jsx(hs,{scope:n,content:S,viewport:b,onViewportChange:I,itemRefCallback:oe,selectedItem:A,onItemLeave:me,itemTextRefCallback:ee,focusSelectedItem:B,selectedItemText:N,position:o,isPositioned:L,searchRef:W,children:m.jsx(Rt,{as:pu,allowPinchZoom:!0,children:m.jsx(bt,{asChild:!0,trapped:y.open,onMountAutoFocus:P=>{P.preventDefault()},onUnmountAutoFocus:M(r,P=>{y.trigger?.focus({preventScroll:!0}),P.preventDefault()}),children:m.jsx(Qe,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:P=>P.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:m.jsx(ve,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:P=>P.preventDefault(),...x,...Z,onPlaced:()=>j(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:M(x.onKeyDown,P=>{const V=P.ctrlKey||P.altKey||P.metaKey;if(P.key==="Tab"&&P.preventDefault(),!V&&P.key.length===1&&Y(P.key),["ArrowUp","ArrowDown","Home","End"].includes(P.key)){let U=F().filter(K=>!K.disabled).map(K=>K.ref.current);if(["ArrowUp","End"].includes(P.key)&&(U=U.slice().reverse()),["ArrowUp","ArrowDown"].includes(P.key)){const K=P.target,G=U.indexOf(K);U=U.slice(G+1)}setTimeout(()=>T(U)),P.preventDefault()}})})})})})})});gs.displayName=fu;var mu="SelectItemAlignedPosition",ws=c.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=Ee(Oe,n),i=Re(Oe,n),[a,l]=c.useState(null),[u,f]=c.useState(null),d=H(t,E=>f(E)),v=Lt(n),h=c.useRef(!1),w=c.useRef(!0),{viewport:p,selectedItem:g,selectedItemText:x,focusSelectedItem:y}=i,S=c.useCallback(()=>{if(s.trigger&&s.valueNode&&a&&u&&p&&g&&x){const E=s.trigger.getBoundingClientRect(),A=u.getBoundingClientRect(),D=s.valueNode.getBoundingClientRect(),N=x.getBoundingClientRect();if(s.dir!=="rtl"){const K=N.left-A.left,G=D.left-K,te=E.left-G,Pe=E.width+te,Ft=Math.max(Pe,A.width),jt=window.innerWidth-re,$t=uo(G,[re,Math.max(re,jt-Ft)]);a.style.minWidth=Pe+"px",a.style.left=$t+"px"}else{const K=A.right-N.right,G=window.innerWidth-D.right-K,te=window.innerWidth-E.right-G,Pe=E.width+te,Ft=Math.max(Pe,A.width),jt=window.innerWidth-re,$t=uo(G,[re,Math.max(re,jt-Ft)]);a.style.minWidth=Pe+"px",a.style.right=$t+"px"}const k=v(),F=window.innerHeight-re*2,L=p.scrollHeight,j=window.getComputedStyle(u),$=parseInt(j.borderTopWidth,10),T=parseInt(j.paddingTop,10),B=parseInt(j.borderBottomWidth,10),_=parseInt(j.paddingBottom,10),R=$+T+L+_+B,W=Math.min(g.offsetHeight*5,R),Y=window.getComputedStyle(p),oe=parseInt(Y.paddingTop,10),me=parseInt(Y.paddingBottom,10),ee=E.top+E.height/2-re,ve=F-ee,Z=g.offsetHeight/2,P=g.offsetTop+Z,V=$+T+P,X=R-V;if(V<=ee){const K=k.length>0&&g===k[k.length-1].ref.current;a.style.bottom="0px";const G=u.clientHeight-p.offsetTop-p.offsetHeight,te=Math.max(ve,Z+(K?me:0)+G+B),Pe=V+te;a.style.height=Pe+"px"}else{const K=k.length>0&&g===k[0].ref.current;a.style.top="0px";const te=Math.max(ee,$+p.offsetTop+(K?oe:0)+Z)+X;a.style.height=te+"px",p.scrollTop=V-ee+p.offsetTop}a.style.margin=`${re}px 0`,a.style.minHeight=W+"px",a.style.maxHeight=F+"px",o?.(),requestAnimationFrame(()=>h.current=!0)}},[v,s.trigger,s.valueNode,a,u,p,g,x,s.dir,o]);z(()=>S(),[S]);const[C,b]=c.useState();z(()=>{u&&b(window.getComputedStyle(u).zIndex)},[u]);const I=c.useCallback(E=>{E&&w.current===!0&&(S(),y?.(),w.current=!1)},[S,y]);return m.jsx(hu,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:I,children:m.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:m.jsx(O.div,{...r,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});ws.displayName=mu;var vu="SelectPopperPosition",sn=c.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=re,...s}=e,i=kt(n);return m.jsx(mr,{...i,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});sn.displayName=vu;var[hu,jn]=Ue(Oe,{}),an="SelectViewport",xs=c.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=Re(an,n),i=jn(an,n),a=H(t,s.onViewportChange),l=c.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),m.jsx(Dt.Slot,{scope:n,children:m.jsx(O.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:M(r.onScroll,u=>{const f=u.currentTarget,{contentWrapper:d,shouldExpandOnScrollRef:v}=i;if(v?.current&&d){const h=Math.abs(l.current-f.scrollTop);if(h>0){const w=window.innerHeight-re*2,p=parseFloat(d.style.minHeight),g=parseFloat(d.style.height),x=Math.max(p,g);if(x<w){const y=x+h,S=Math.min(w,y),C=y-S;d.style.height=S+"px",d.style.bottom==="0px"&&(f.scrollTop=C>0?C:0,d.style.justifyContent="flex-end")}}}l.current=f.scrollTop})})})]})});xs.displayName=an;var ys="SelectGroup",[gu,wu]=Ue(ys),Ss=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=ue();return m.jsx(gu,{scope:n,id:r,children:m.jsx(O.div,{role:"group","aria-labelledby":r,...o,ref:t})})});Ss.displayName=ys;var Cs="SelectLabel",bs=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=wu(Cs,n);return m.jsx(O.div,{id:r.id,...o,ref:t})});bs.displayName=Cs;var xt="SelectItem",[xu,Es]=Ue(xt),Rs=c.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...i}=e,a=Ee(xt,n),l=Re(xt,n),u=a.value===o,[f,d]=c.useState(s??""),[v,h]=c.useState(!1),w=H(t,y=>l.itemRefCallback?.(y,o,r)),p=ue(),g=c.useRef("touch"),x=()=>{r||(a.onValueChange(o),a.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(xu,{scope:n,value:o,disabled:r,textId:p,isSelected:u,onItemTextChange:c.useCallback(y=>{d(S=>S||(y?.textContent??"").trim())},[]),children:m.jsx(Dt.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:m.jsx(O.div,{role:"option","aria-labelledby":p,"data-highlighted":v?"":void 0,"aria-selected":u&&v,"data-state":u?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...i,ref:w,onFocus:M(i.onFocus,()=>h(!0)),onBlur:M(i.onBlur,()=>h(!1)),onClick:M(i.onClick,()=>{g.current!=="mouse"&&x()}),onPointerUp:M(i.onPointerUp,()=>{g.current==="mouse"&&x()}),onPointerDown:M(i.onPointerDown,y=>{g.current=y.pointerType}),onPointerMove:M(i.onPointerMove,y=>{g.current=y.pointerType,r?l.onItemLeave?.():g.current==="mouse"&&y.currentTarget.focus({preventScroll:!0})}),onPointerLeave:M(i.onPointerLeave,y=>{y.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:M(i.onKeyDown,y=>{l.searchRef?.current!==""&&y.key===" "||(su.includes(y.key)&&x(),y.key===" "&&y.preventDefault())})})})})});Rs.displayName=xt;var Ge="SelectItemText",Ps=c.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,i=Ee(Ge,n),a=Re(Ge,n),l=Es(Ge,n),u=lu(Ge,n),[f,d]=c.useState(null),v=H(t,x=>d(x),l.onItemTextChange,x=>a.itemTextRefCallback?.(x,l.value,l.disabled)),h=f?.textContent,w=c.useMemo(()=>m.jsx("option",{value:l.value,disabled:l.disabled,children:h},l.value),[l.disabled,l.value,h]),{onNativeOptionAdd:p,onNativeOptionRemove:g}=u;return z(()=>(p(w),()=>g(w)),[p,g,w]),m.jsxs(m.Fragment,{children:[m.jsx(O.span,{id:l.textId,...s,ref:v}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Ze.createPortal(s.children,i.valueNode):null]})});Ps.displayName=Ge;var Ms="SelectItemIndicator",As=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return Es(Ms,n).isSelected?m.jsx(O.span,{"aria-hidden":!0,...o,ref:t}):null});As.displayName=Ms;var cn="SelectScrollUpButton",Is=c.forwardRef((e,t)=>{const n=Re(cn,e.__scopeSelect),o=jn(cn,e.__scopeSelect),[r,s]=c.useState(!1),i=H(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let a=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),r?m.jsx(Ts,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop-l.offsetHeight)}}):null});Is.displayName=cn;var ln="SelectScrollDownButton",_s=c.forwardRef((e,t)=>{const n=Re(ln,e.__scopeSelect),o=jn(ln,e.__scopeSelect),[r,s]=c.useState(!1),i=H(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let a=function(){const u=l.scrollHeight-l.clientHeight,f=Math.ceil(l.scrollTop)<u;s(f)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),r?m.jsx(Ts,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop+l.offsetHeight)}}):null});_s.displayName=ln;var Ts=c.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=Re("SelectScrollButton",n),i=c.useRef(null),a=Lt(n),l=c.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return c.useEffect(()=>()=>l(),[l]),z(()=>{a().find(f=>f.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[a]),m.jsx(O.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:M(r.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(o,50))}),onPointerMove:M(r.onPointerMove,()=>{s.onItemLeave?.(),i.current===null&&(i.current=window.setInterval(o,50))}),onPointerLeave:M(r.onPointerLeave,()=>{l()})})}),yu="SelectSeparator",Os=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return m.jsx(O.div,{"aria-hidden":!0,...o,ref:t})});Os.displayName=yu;var un="SelectArrow",Su=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=kt(n),s=Ee(un,n),i=Re(un,n);return s.open&&i.position==="popper"?m.jsx(vr,{...r,...o,ref:t}):null});Su.displayName=un;var Cu="SelectBubbleInput",Ns=c.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=c.useRef(null),s=H(o,r),i=ou(t);return c.useEffect(()=>{const a=r.current;if(!a)return;const l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==t&&f){const d=new Event("change",{bubbles:!0});f.call(a,t),a.dispatchEvent(d)}},[i,t]),m.jsx(O.select,{...n,style:{...wo,...n.style},ref:s,defaultValue:t})});Ns.displayName=Cu;function Ds(e){return e===""||e===void 0}function Ls(e){const t=se(e),n=c.useRef(""),o=c.useRef(0),r=c.useCallback(i=>{const a=n.current+i;t(a),function l(u){n.current=u,window.clearTimeout(o.current),u!==""&&(o.current=window.setTimeout(()=>l(""),1e3))}(a)},[t]),s=c.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return c.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function ks(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=bu(e,Math.max(s,0));r.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function bu(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var nd=cs,od=us,rd=fs,sd=ps,id=ms,ad=vs,cd=xs,ld=Ss,ud=bs,dd=Rs,fd=Ps,pd=As,md=Is,vd=_s,hd=Os;export{ld as $,pr as A,Au as B,pi as C,$u as D,Gu as E,Vu as F,zu as G,Yu as H,Xu as I,Ju as J,qu as K,Zu as L,Qu as M,ed as N,ku as O,ye as P,nd as Q,Mu as R,Ru as S,ju as T,od as U,xo as V,sd as W,rd as X,id as Y,ad as Z,cd as _,xe as a,ud as a0,dd as a1,pd as a2,fd as a3,hd as a4,md as a5,vd as a6,M as b,dn as c,se as d,O as e,H as f,St as g,z as h,mo as i,Tu as j,mi as k,Du as l,Fu as m,Bu as n,Lu as o,_t as p,ue as q,Ze as r,fr as s,vr as t,Be as u,Qe as v,mr as w,Pu as x,Iu as y,Hu as z};
//# sourceMappingURL=radix-Jg4_14Md.js.map
