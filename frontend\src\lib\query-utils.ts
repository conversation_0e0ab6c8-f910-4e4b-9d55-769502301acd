import {
  type FilterCondition,
  type FilterGroup,
  type FilterOperator,
  type LogicalOperator,
  type QueryParametersDto,
  type SortInfo,
} from '@/client'

// Define interfaces for API error handling
interface ApiErrorDetails {
  status?: number;
  title?: string;
  detail?: string;
  message?: string;
  error?: {
    message?: string;
    details?: string;
  };
  [key: string]: unknown;
}

interface ApiError {
  error?: string;
  message?: string;
  details?: ApiErrorDetails;
  status?: number;
  url?: string;
}

// Define interface for sorting structure
interface SortingItem {
  id: string;
  desc: boolean;
}

/**
 * Interface for parameters needed to generate a query body
 */
export interface QueryBuilderParams {
  pageIndex: number
  pageSize: number
  filter?: string
  sorting?: string
  filterField?: string
  operator?: FilterOperator
}

/**
 * Generates a query parameters object for API list requests
 *
 * @param params Query parameters containing pagination, filtering, and sorting info
 * @returns A query parameters object ready to be used in API requests
 */
export function generateQueryParameters(params: QueryBuilderParams): QueryParametersDto {
  const {
    pageIndex,
    pageSize,
    filter,
    sorting,
    filterField = 'clientId',
    operator = 'Contains',
  } = params

  // Skip calculation is handled by the API through the page parameter

  // Ensure Sorting is never empty string
  const sortingValue = sorting ?? ''

  // Create filter conditions if filter is provided
  const filterConditions: FilterCondition[] = []
  if (filter && filter !== 'null') {
    filterConditions.push({
      fieldName: filterField,
      operator: operator,
      value: filter,
    })
  }

  // Create filter group
  const filterGroup: FilterGroup = {
    operator: 'And' as LogicalOperator,
    conditions: filterConditions,
  }

  // Convert sorting string to SortInfo array
  const sortArray: Array<SortInfo> = []
  if (sorting) {
    try {
      const parsedSorting = JSON.parse(sorting) as SortingItem[]
      parsedSorting.forEach((sort) => {
        sortArray.push({
          field: sort.id,
          desc: sort.desc,
        })
      })
    } catch (e) {
      console.error('Error parsing sorting JSON:', e)
    }
  }

  // Return query parameters object
  return {
    sorting: sortingValue,
    page: pageIndex + 1,
    sort: sortArray,
    filterGroup: filterGroup,
    maxResultCount: pageSize,
  } as QueryParametersDto
}

/**
 * Handles API errors consistently
 *
 * @param error The error object from the API
 * @param defaultTitle Default error title if not provided in error
 * @param defaultDescription Default error description if not provided in error
 * @returns A standard error object with title and description
 */
export function extractApiError(
  error: unknown,
  defaultTitle = 'Error loading data',
  defaultDescription = 'An unexpected error occurred'
): { title: string; description: string } {
  let title = defaultTitle
  let description = defaultDescription

  try {
    const apiError = error as ApiError
    if (apiError?.details?.error) {
      title = apiError.details.error.message ?? defaultTitle
      description = apiError.details.error.details ?? defaultDescription
    }
  } catch (e) {
    console.error('Error parsing API error:', e)
  }

  return {
    title,
    description,
  }
}
