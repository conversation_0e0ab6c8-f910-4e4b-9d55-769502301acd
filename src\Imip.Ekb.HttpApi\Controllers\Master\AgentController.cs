using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Agent;
using Imip.Ekb.Master.Agent.Dtos;
using Imip.Ekb.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Controllers.Master;

[Route("api/master/agent")]
[RemoteService]
[Authorize] // Require authentication for all endpoints
public class AgentController : AbpControllerBase
{
    private readonly IAgentAppService _agentAppService;

    public AgentController(IAgentAppService agentAppService)
    {
        _agentAppService = agentAppService;
    }

    /// <summary>
    /// Get a paginated list of agents with filtering support
    /// </summary>
    /// <param name="input">Query parameters for filtering and pagination</param>
    /// <returns>Paginated list of agents</returns>
    [HttpPost("filter-list")]
    public virtual async Task<PagedResultDto<AgentDto>> GetFilteredListAsync(QueryParametersDto input)
    {
        return await _agentAppService.FilterListAsync(input);
    }

    /// <summary>
    /// Get a paginated list of agents
    /// </summary>
    /// <param name="input">Pagination and sorting parameters</param>
    /// <returns>Paginated list of agents</returns>
    [HttpGet]
    public virtual async Task<PagedResultDto<AgentDto>> GetListAsync([FromQuery] PagedAndSortedResultRequestDto input)
    {
        return await _agentAppService.GetListAsync(input);
    }

    /// <summary>
    /// Get a specific agent by ID
    /// </summary>
    /// <param name="id">Agent ID</param>
    /// <returns>Agent details</returns>
    [HttpGet("{id}")]
    public virtual async Task<AgentDto> GetAsync(Guid id)
    {
        return await _agentAppService.GetAsync(id);
    }

    /// <summary>
    /// Create a new agent
    /// </summary>
    /// <param name="input">Agent creation data</param>
    /// <returns>Created agent</returns>
    [HttpPost]
    public virtual async Task<AgentDto> CreateAsync(AgentCreateUpdateDto input)
    {
        return await _agentAppService.CreateAsync(input);
    }

    /// <summary>
    /// Update an existing agent
    /// </summary>
    /// <param name="id">Agent ID</param>
    /// <param name="input">Agent update data</param>
    /// <returns>Updated agent</returns>
    [HttpPut("{id}")]
    public virtual async Task<AgentDto> UpdateAsync(Guid id, AgentCreateUpdateDto input)
    {
        return await _agentAppService.UpdateAsync(id, input);
    }

    /// <summary>
    /// Delete an agent
    /// </summary>
    /// <param name="id">Agent ID</param>
    [HttpDelete("{id}")]
    public virtual async Task DeleteAsync(Guid id)
    {
        await _agentAppService.DeleteAsync(id);
    }
}
