using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.DestinationPort.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.DestinationPort;

[Authorize]
public class DestinationPortAppService :
    CrudAppService<DestinationPort, DestinationPortDto, Guid, PagedAndSortedResultRequestDto, DestinationPortCreateUpdateDto, DestinationPortCreateUpdateDto>,
    IDestinationPortAppService
{
    private readonly IDestinationPortRepository _destinationPortRepository;
    private readonly DestinationPortMapper _mapper;
    private readonly ILogger<DestinationPortAppService> _logger;

    public DestinationPortAppService(
        IDestinationPortRepository destinationPortRepository,
        DestinationPortMapper mapper,
        ILogger<DestinationPortAppService> logger)
        : base(destinationPortRepository)
    {
        _destinationPortRepository = destinationPortRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<DestinationPortDto> CreateAsync(DestinationPortCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _destinationPortRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<DestinationPortDto> UpdateAsync(Guid id, DestinationPortCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _destinationPortRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _destinationPortRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _destinationPortRepository.GetAsync(id);
        entity.Deleted = "Y";
        entity.UpdatedAt = Clock.Now;
        await _destinationPortRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<DestinationPortDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _destinationPortRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<DestinationPortDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _destinationPortRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(DestinationPort.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<DestinationPortDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<DestinationPortDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _destinationPortRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<DestinationPortDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<DestinationPort> ApplyDynamicQuery(IQueryable<DestinationPort> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<DestinationPort>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<DestinationPort>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}