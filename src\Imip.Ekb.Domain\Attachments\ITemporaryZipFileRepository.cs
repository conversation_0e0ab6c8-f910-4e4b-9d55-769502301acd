using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Attachments
{
    /// <summary>
    /// Repository interface for TemporaryZipFile entity
    /// </summary>
    public interface ITemporaryZipFileRepository : IRepository<TemporaryZipFile, Guid>
    {
        /// <summary>
        /// Gets temporary ZIP files that are due for deletion
        /// </summary>
        /// <param name="now">The current time</param>
        /// <returns>List of temporary ZIP files</returns>
        Task<List<TemporaryZipFile>> GetDueForDeletionAsync(DateTime now);

        /// <summary>
        /// Marks a temporary ZIP file as processed
        /// </summary>
        /// <param name="id">The ID of the temporary ZIP file</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task MarkAsProcessedAsync(Guid id);
    }
}
