using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Trading.Dtos;
using Imip.Ekb.Models;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Trading;

[Authorize]
public class TradingAppService :
    CrudAppService<Trading, TradingDto, Guid, PagedAndSortedResultRequestDto, TradingCreateUpdateDto, TradingCreateUpdateDto>,
    ITradingAppService
{
    private readonly ITradingRepository _tradingRepository;
    private readonly TradingMapper _mapper;
    private readonly ILogger<TradingAppService> _logger;

    public TradingAppService(
        ITradingRepository tradingRepository,
        TradingMapper mapper,
        ILogger<TradingAppService> logger)
        : base(tradingRepository)
    {
        _tradingRepository = tradingRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<TradingDto> CreateAsync(TradingCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _tradingRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<TradingDto> UpdateAsync(Guid id, TradingCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _tradingRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _tradingRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _tradingRepository.GetAsync(id);
        entity.IsActive = "N";
        entity.UpdatedAt = Clock.Now;
        await _tradingRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<TradingDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _tradingRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<TradingDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _tradingRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Trading.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<TradingDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<TradingDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _tradingRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<TradingDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<Trading> ApplyDynamicQuery(IQueryable<Trading> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Trading>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Trading>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}