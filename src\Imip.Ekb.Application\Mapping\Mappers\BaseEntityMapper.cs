﻿using System;
using Imip.Ekb.Contracts;
using Riok.Mapperly.Abstractions;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public abstract partial class BaseEntityMapper
{
    protected IMappingContext Context { get; }

    protected BaseEntityMapper(IMappingContext context)
    {
        Context = context;
    }

    // Full Audited Entity Mapping
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.Id))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.CreationTime))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.CreatorId))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.LastModificationTime))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.LastModifierId))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.IsDeleted))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.DeleterId))]
    [MapperIgnoreSource(nameof(FullAuditedEntity<Guid>.DeletionTime))]
    protected virtual TEntity MapToFullAuditedEntity<TDto, TEntity>(TDto dto, TEntity entity)
        where TEntity : FullAuditedEntity<Guid>
    {
        var mapped = MapCore(dto, entity);

        // Note: Audit properties are typically set by ABP Framework automatically
        // during entity creation/update operations. This method focuses on mapping
        // business properties while letting ABP handle audit properties.

        return mapped;
    }

    // Audited Entity Mapping
    [MapperIgnoreSource(nameof(AuditedEntity<Guid>.Id))]
    [MapperIgnoreSource(nameof(AuditedEntity<Guid>.CreationTime))]
    [MapperIgnoreSource(nameof(AuditedEntity<Guid>.CreatorId))]
    [MapperIgnoreSource(nameof(AuditedEntity<Guid>.LastModificationTime))]
    [MapperIgnoreSource(nameof(AuditedEntity<Guid>.LastModifierId))]
    protected virtual TEntity MapToAuditedEntity<TDto, TEntity>(TDto dto, TEntity entity)
        where TEntity : AuditedEntity<Guid>
    {
        var mapped = MapCore(dto, entity);

        // Note: Audit properties are typically set by ABP Framework automatically
        // during entity creation/update operations. This method focuses on mapping
        // business properties while letting ABP handle audit properties.

        return mapped;
    }

    // Creation Audited Entity Mapping
    [MapperIgnoreSource(nameof(CreationAuditedEntity<Guid>.Id))]
    [MapperIgnoreSource(nameof(CreationAuditedEntity<Guid>.CreationTime))]
    [MapperIgnoreSource(nameof(CreationAuditedEntity<Guid>.CreatorId))]
    protected virtual TEntity MapToCreationAuditedEntity<TDto, TEntity>(TDto dto, TEntity entity)
        where TEntity : CreationAuditedEntity<Guid>
    {
        var mapped = MapCore(dto, entity);

        // Note: Audit properties are typically set by ABP Framework automatically
        // during entity creation/update operations. This method focuses on mapping
        // business properties while letting ABP handle audit properties.

        return mapped;
    }

    // Generic entity mapping with proper constraints
    protected virtual TEntity MapToEntity<TDto, TEntity>(TDto dto, TEntity entity)
        where TEntity : IEntity<Guid>
    {
        var mapped = MapCore(dto, entity);

        // Note: Audit properties are typically set by ABP Framework automatically
        // during entity creation/update operations. This method focuses on mapping
        // business properties while letting ABP handle audit properties.

        return mapped;
    }

    protected abstract TEntity MapCore<TDto, TEntity>(TDto dto, TEntity entity);
}