var Se=e=>{throw TypeError(e)};var ee=(e,t,r)=>t.has(e)||Se("Cannot "+r);var n=(e,t,r)=>(ee(e,t,"read from private field"),r?r.call(e):t.get(e)),v=(e,t,r)=>t.has(e)?Se("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),l=(e,t,r,s)=>(ee(e,t,"write to private field"),s?s.call(e,r):t.set(e,r),r),O=(e,t,r)=>(ee(e,t,"access private method"),r);var Xt=(e,t,r,s)=>({set _(u){l(e,t,u,r)},get _(){return n(e,t,s)}});function fr(e,t){for(var r=0;r<t.length;r++){const s=t[r];if(typeof s!="string"&&!Array.isArray(s)){for(const u in s)if(u!=="default"&&!(u in e)){const a=Object.getOwnPropertyDescriptor(s,u);a&&Object.defineProperty(e,u,a.get?a:{enumerable:!0,get:()=>s[u]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var Zr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function dr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ts(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if(typeof t=="function"){var r=function s(){return this instanceof s?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(s){var u=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(r,s,u.get?u:{enumerable:!0,get:function(){return e[s]}})}),r}var re={exports:{}},Ht={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var we;function pr(){if(we)return Ht;we=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(s,u,a){var f=null;if(a!==void 0&&(f=""+a),u.key!==void 0&&(f=""+u.key),"key"in u){a={};for(var d in u)d!=="key"&&(a[d]=u[d])}else a=u;return u=a.ref,{$$typeof:e,type:s,key:f,ref:u!==void 0?u:null,props:a}}return Ht.Fragment=t,Ht.jsx=r,Ht.jsxs=r,Ht}var Pe;function yr(){return Pe||(Pe=1,re.exports=pr()),re.exports}var vr=yr(),se={exports:{}},b={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Te;function mr(){if(Te)return b;Te=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),a=Symbol.for("react.consumer"),f=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),o=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),y=Symbol.iterator;function Q(i){return i===null||typeof i!="object"?null:(i=y&&i[y]||i["@@iterator"],typeof i=="function"?i:null)}var P={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},F=Object.assign,w={};function T(i,c,m){this.props=i,this.context=c,this.refs=w,this.updater=m||P}T.prototype.isReactComponent={},T.prototype.setState=function(i,c){if(typeof i!="object"&&typeof i!="function"&&i!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,i,c,"setState")},T.prototype.forceUpdate=function(i){this.updater.enqueueForceUpdate(this,i,"forceUpdate")};function j(){}j.prototype=T.prototype;function L(i,c,m){this.props=i,this.context=c,this.refs=w,this.updater=m||P}var K=L.prototype=new j;K.constructor=L,F(K,T.prototype),K.isPureReactComponent=!0;var z=Array.isArray,C={H:null,A:null,T:null,S:null,V:null},nt=Object.prototype.hasOwnProperty;function Jt(i,c,m,g,_,D){return m=D.ref,{$$typeof:e,type:i,key:c,ref:m!==void 0?m:null,props:D}}function Y(i,c){return Jt(i.type,c,void 0,void 0,void 0,i.props)}function I(i){return typeof i=="object"&&i!==null&&i.$$typeof===e}function Pt(i){var c={"=":"=0",":":"=2"};return"$"+i.replace(/[=:]/g,function(m){return c[m]})}var et=/\/+/g;function it(i,c){return typeof i=="object"&&i!==null&&i.key!=null?Pt(""+i.key):c.toString(36)}function Ce(){}function ar(i){switch(i.status){case"fulfilled":return i.value;case"rejected":throw i.reason;default:switch(typeof i.status=="string"?i.then(Ce,Ce):(i.status="pending",i.then(function(c){i.status==="pending"&&(i.status="fulfilled",i.value=c)},function(c){i.status==="pending"&&(i.status="rejected",i.reason=c)})),i.status){case"fulfilled":return i.value;case"rejected":throw i.reason}}throw i}function Tt(i,c,m,g,_){var D=typeof i;(D==="undefined"||D==="boolean")&&(i=null);var R=!1;if(i===null)R=!0;else switch(D){case"bigint":case"string":case"number":R=!0;break;case"object":switch(i.$$typeof){case e:case t:R=!0;break;case p:return R=i._init,Tt(R(i._payload),c,m,g,_)}}if(R)return _=_(i),R=g===""?"."+it(i,0):g,z(_)?(m="",R!=null&&(m=R.replace(et,"$&/")+"/"),Tt(_,c,m,"",function(lr){return lr})):_!=null&&(I(_)&&(_=Y(_,m+(_.key==null||i&&i.key===_.key?"":(""+_.key).replace(et,"$&/")+"/")+R)),c.push(_)),1;R=0;var ot=g===""?".":g+":";if(z(i))for(var M=0;M<i.length;M++)g=i[M],D=ot+it(g,M),R+=Tt(g,c,m,D,_);else if(M=Q(i),typeof M=="function")for(i=M.call(i),M=0;!(g=i.next()).done;)g=g.value,D=ot+it(g,M++),R+=Tt(g,c,m,D,_);else if(D==="object"){if(typeof i.then=="function")return Tt(ar(i),c,m,g,_);throw c=String(i),Error("Objects are not valid as a React child (found: "+(c==="[object Object]"?"object with keys {"+Object.keys(i).join(", ")+"}":c)+"). If you meant to render a collection of children, use an array instead.")}return R}function Vt(i,c,m){if(i==null)return i;var g=[],_=0;return Tt(i,g,"","",function(D){return c.call(m,D,_++)}),g}function cr(i){if(i._status===-1){var c=i._result;c=c(),c.then(function(m){(i._status===0||i._status===-1)&&(i._status=1,i._result=m)},function(m){(i._status===0||i._status===-1)&&(i._status=2,i._result=m)}),i._status===-1&&(i._status=0,i._result=c)}if(i._status===1)return i._result.default;throw i._result}var _e=typeof reportError=="function"?reportError:function(i){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var c=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof i=="object"&&i!==null&&typeof i.message=="string"?String(i.message):String(i),error:i});if(!window.dispatchEvent(c))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",i);return}};function hr(){}return b.Children={map:Vt,forEach:function(i,c,m){Vt(i,function(){c.apply(this,arguments)},m)},count:function(i){var c=0;return Vt(i,function(){c++}),c},toArray:function(i){return Vt(i,function(c){return c})||[]},only:function(i){if(!I(i))throw Error("React.Children.only expected to receive a single React element child.");return i}},b.Component=T,b.Fragment=r,b.Profiler=u,b.PureComponent=L,b.StrictMode=s,b.Suspense=h,b.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=C,b.__COMPILER_RUNTIME={__proto__:null,c:function(i){return C.H.useMemoCache(i)}},b.cache=function(i){return function(){return i.apply(null,arguments)}},b.cloneElement=function(i,c,m){if(i==null)throw Error("The argument must be a React element, but you passed "+i+".");var g=F({},i.props),_=i.key,D=void 0;if(c!=null)for(R in c.ref!==void 0&&(D=void 0),c.key!==void 0&&(_=""+c.key),c)!nt.call(c,R)||R==="key"||R==="__self"||R==="__source"||R==="ref"&&c.ref===void 0||(g[R]=c[R]);var R=arguments.length-2;if(R===1)g.children=m;else if(1<R){for(var ot=Array(R),M=0;M<R;M++)ot[M]=arguments[M+2];g.children=ot}return Jt(i.type,_,void 0,void 0,D,g)},b.createContext=function(i){return i={$$typeof:f,_currentValue:i,_currentValue2:i,_threadCount:0,Provider:null,Consumer:null},i.Provider=i,i.Consumer={$$typeof:a,_context:i},i},b.createElement=function(i,c,m){var g,_={},D=null;if(c!=null)for(g in c.key!==void 0&&(D=""+c.key),c)nt.call(c,g)&&g!=="key"&&g!=="__self"&&g!=="__source"&&(_[g]=c[g]);var R=arguments.length-2;if(R===1)_.children=m;else if(1<R){for(var ot=Array(R),M=0;M<R;M++)ot[M]=arguments[M+2];_.children=ot}if(i&&i.defaultProps)for(g in R=i.defaultProps,R)_[g]===void 0&&(_[g]=R[g]);return Jt(i,D,void 0,void 0,null,_)},b.createRef=function(){return{current:null}},b.forwardRef=function(i){return{$$typeof:d,render:i}},b.isValidElement=I,b.lazy=function(i){return{$$typeof:p,_payload:{_status:-1,_result:i},_init:cr}},b.memo=function(i,c){return{$$typeof:o,type:i,compare:c===void 0?null:c}},b.startTransition=function(i){var c=C.T,m={};C.T=m;try{var g=i(),_=C.S;_!==null&&_(m,g),typeof g=="object"&&g!==null&&typeof g.then=="function"&&g.then(hr,_e)}catch(D){_e(D)}finally{C.T=c}},b.unstable_useCacheRefresh=function(){return C.H.useCacheRefresh()},b.use=function(i){return C.H.use(i)},b.useActionState=function(i,c,m){return C.H.useActionState(i,c,m)},b.useCallback=function(i,c){return C.H.useCallback(i,c)},b.useContext=function(i){return C.H.useContext(i)},b.useDebugValue=function(){},b.useDeferredValue=function(i,c){return C.H.useDeferredValue(i,c)},b.useEffect=function(i,c,m){var g=C.H;if(typeof m=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return g.useEffect(i,c)},b.useId=function(){return C.H.useId()},b.useImperativeHandle=function(i,c,m){return C.H.useImperativeHandle(i,c,m)},b.useInsertionEffect=function(i,c){return C.H.useInsertionEffect(i,c)},b.useLayoutEffect=function(i,c){return C.H.useLayoutEffect(i,c)},b.useMemo=function(i,c){return C.H.useMemo(i,c)},b.useOptimistic=function(i,c){return C.H.useOptimistic(i,c)},b.useReducer=function(i,c,m){return C.H.useReducer(i,c,m)},b.useRef=function(i){return C.H.useRef(i)},b.useState=function(i){return C.H.useState(i)},b.useSyncExternalStore=function(i,c,m){return C.H.useSyncExternalStore(i,c,m)},b.useTransition=function(){return C.H.useTransition()},b.version="19.1.0",b}var Ae;function Je(){return Ae||(Ae=1,se.exports=mr()),se.exports}var G=Je();const gr=dr(G),es=fr({__proto__:null,default:gr},[G]);var ne={exports:{}},q={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fe;function br(){if(Fe)return q;Fe=1;var e=Je();function t(h){var o="https://react.dev/errors/"+h;if(1<arguments.length){o+="?args[]="+encodeURIComponent(arguments[1]);for(var p=2;p<arguments.length;p++)o+="&args[]="+encodeURIComponent(arguments[p])}return"Minified React error #"+h+"; visit "+o+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(t(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},u=Symbol.for("react.portal");function a(h,o,p){var y=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:y==null?null:""+y,children:h,containerInfo:o,implementation:p}}var f=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function d(h,o){if(h==="font")return"";if(typeof o=="string")return o==="use-credentials"?o:""}return q.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,q.createPortal=function(h,o){var p=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!o||o.nodeType!==1&&o.nodeType!==9&&o.nodeType!==11)throw Error(t(299));return a(h,o,null,p)},q.flushSync=function(h){var o=f.T,p=s.p;try{if(f.T=null,s.p=2,h)return h()}finally{f.T=o,s.p=p,s.d.f()}},q.preconnect=function(h,o){typeof h=="string"&&(o?(o=o.crossOrigin,o=typeof o=="string"?o==="use-credentials"?o:"":void 0):o=null,s.d.C(h,o))},q.prefetchDNS=function(h){typeof h=="string"&&s.d.D(h)},q.preinit=function(h,o){if(typeof h=="string"&&o&&typeof o.as=="string"){var p=o.as,y=d(p,o.crossOrigin),Q=typeof o.integrity=="string"?o.integrity:void 0,P=typeof o.fetchPriority=="string"?o.fetchPriority:void 0;p==="style"?s.d.S(h,typeof o.precedence=="string"?o.precedence:void 0,{crossOrigin:y,integrity:Q,fetchPriority:P}):p==="script"&&s.d.X(h,{crossOrigin:y,integrity:Q,fetchPriority:P,nonce:typeof o.nonce=="string"?o.nonce:void 0})}},q.preinitModule=function(h,o){if(typeof h=="string")if(typeof o=="object"&&o!==null){if(o.as==null||o.as==="script"){var p=d(o.as,o.crossOrigin);s.d.M(h,{crossOrigin:p,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0})}}else o==null&&s.d.M(h)},q.preload=function(h,o){if(typeof h=="string"&&typeof o=="object"&&o!==null&&typeof o.as=="string"){var p=o.as,y=d(p,o.crossOrigin);s.d.L(h,p,{crossOrigin:y,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0,type:typeof o.type=="string"?o.type:void 0,fetchPriority:typeof o.fetchPriority=="string"?o.fetchPriority:void 0,referrerPolicy:typeof o.referrerPolicy=="string"?o.referrerPolicy:void 0,imageSrcSet:typeof o.imageSrcSet=="string"?o.imageSrcSet:void 0,imageSizes:typeof o.imageSizes=="string"?o.imageSizes:void 0,media:typeof o.media=="string"?o.media:void 0})}},q.preloadModule=function(h,o){if(typeof h=="string")if(o){var p=d(o.as,o.crossOrigin);s.d.m(h,{as:typeof o.as=="string"&&o.as!=="script"?o.as:void 0,crossOrigin:p,integrity:typeof o.integrity=="string"?o.integrity:void 0})}else s.d.m(h)},q.requestFormReset=function(h){s.d.r(h)},q.unstable_batchedUpdates=function(h,o){return h(o)},q.useFormState=function(h,o,p){return f.H.useFormState(h,o,p)},q.useFormStatus=function(){return f.H.useHostTransitionStatus()},q.version="19.1.0",q}var De;function rs(){if(De)return ne.exports;De=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch{}}return e(),ne.exports=br(),ne.exports}var Wt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},wt=typeof window>"u"||"Deno"in globalThis;function $(){}function Rr(e,t){return typeof e=="function"?e(t):e}function oe(e){return typeof e=="number"&&e>=0&&e!==1/0}function Ve(e,t){return Math.max(e+(t||0)-Date.now(),0)}function vt(e,t){return typeof e=="function"?e(t):e}function V(e,t){return typeof e=="function"?e(t):e}function Me(e,t){const{type:r="all",exact:s,fetchStatus:u,predicate:a,queryKey:f,stale:d}=e;if(f){if(s){if(t.queryHash!==be(f,t.options))return!1}else if(!Gt(t.queryKey,f))return!1}if(r!=="all"){const h=t.isActive();if(r==="active"&&!h||r==="inactive"&&h)return!1}return!(typeof d=="boolean"&&t.isStale()!==d||u&&u!==t.state.fetchStatus||a&&!a(t))}function Qe(e,t){const{exact:r,status:s,predicate:u,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(r){if($t(t.options.mutationKey)!==$t(a))return!1}else if(!Gt(t.options.mutationKey,a))return!1}return!(s&&t.state.status!==s||u&&!u(t))}function be(e,t){return(t?.queryKeyHashFn||$t)(e)}function $t(e){return JSON.stringify(e,(t,r)=>ae(r)?Object.keys(r).sort().reduce((s,u)=>(s[u]=r[u],s),{}):r)}function Gt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(r=>Gt(e[r],t[r])):!1}function Xe(e,t){if(e===t)return e;const r=xe(e)&&xe(t);if(r||ae(e)&&ae(t)){const s=r?e:Object.keys(e),u=s.length,a=r?t:Object.keys(t),f=a.length,d=r?[]:{},h=new Set(s);let o=0;for(let p=0;p<f;p++){const y=r?p:a[p];(!r&&h.has(y)||r)&&e[y]===void 0&&t[y]===void 0?(d[y]=void 0,o++):(d[y]=Xe(e[y],t[y]),d[y]===e[y]&&e[y]!==void 0&&o++)}return u===f&&o===u?e:d}return t}function ue(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function xe(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function ae(e){if(!je(e))return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(!je(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function je(e){return Object.prototype.toString.call(e)==="[object Object]"}function Or(e){return new Promise(t=>{setTimeout(t,e)})}function ce(e,t,r){return typeof r.structuralSharing=="function"?r.structuralSharing(e,t):r.structuralSharing!==!1?Xe(e,t):t}function Er(e,t,r=0){const s=[...e,t];return r&&s.length>r?s.slice(1):s}function Cr(e,t,r=0){const s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var Re=Symbol();function Ze(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:!e.queryFn||e.queryFn===Re?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function _r(e,t){return typeof e=="function"?e(...t):!!e}var mt,at,At,He,Sr=(He=class extends Wt{constructor(){super();v(this,mt);v(this,at);v(this,At);l(this,At,t=>{if(!wt&&window.addEventListener){const r=()=>t();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}})}onSubscribe(){n(this,at)||this.setEventListener(n(this,At))}onUnsubscribe(){var t;this.hasListeners()||((t=n(this,at))==null||t.call(this),l(this,at,void 0))}setEventListener(t){var r;l(this,At,t),(r=n(this,at))==null||r.call(this),l(this,at,t(s=>{typeof s=="boolean"?this.setFocused(s):this.onFocus()}))}setFocused(t){n(this,mt)!==t&&(l(this,mt,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(r=>{r(t)})}isFocused(){return typeof n(this,mt)=="boolean"?n(this,mt):globalThis.document?.visibilityState!=="hidden"}},mt=new WeakMap,at=new WeakMap,At=new WeakMap,He),Oe=new Sr,Ft,ct,Dt,Ne,wr=(Ne=class extends Wt{constructor(){super();v(this,Ft,!0);v(this,ct);v(this,Dt);l(this,Dt,t=>{if(!wt&&window.addEventListener){const r=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",s)}}})}onSubscribe(){n(this,ct)||this.setEventListener(n(this,Dt))}onUnsubscribe(){var t;this.hasListeners()||((t=n(this,ct))==null||t.call(this),l(this,ct,void 0))}setEventListener(t){var r;l(this,Dt,t),(r=n(this,ct))==null||r.call(this),l(this,ct,t(this.setOnline.bind(this)))}setOnline(t){n(this,Ft)!==t&&(l(this,Ft,t),this.listeners.forEach(s=>{s(t)}))}isOnline(){return n(this,Ft)}},Ft=new WeakMap,ct=new WeakMap,Dt=new WeakMap,Ne),te=new wr;function he(){let e,t;const r=new Promise((u,a)=>{e=u,t=a});r.status="pending",r.catch(()=>{});function s(u){Object.assign(r,u),delete r.resolve,delete r.reject}return r.resolve=u=>{s({status:"fulfilled",value:u}),e(u)},r.reject=u=>{s({status:"rejected",reason:u}),t(u)},r}function Pr(e){return Math.min(1e3*2**e,3e4)}function tr(e){return(e??"online")==="online"?te.isOnline():!0}var er=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function ie(e){return e instanceof er}function rr(e){let t=!1,r=0,s=!1,u;const a=he(),f=w=>{s||(Q(new er(w)),e.abort?.())},d=()=>{t=!0},h=()=>{t=!1},o=()=>Oe.isFocused()&&(e.networkMode==="always"||te.isOnline())&&e.canRun(),p=()=>tr(e.networkMode)&&e.canRun(),y=w=>{s||(s=!0,e.onSuccess?.(w),u?.(),a.resolve(w))},Q=w=>{s||(s=!0,e.onError?.(w),u?.(),a.reject(w))},P=()=>new Promise(w=>{u=T=>{(s||o())&&w(T)},e.onPause?.()}).then(()=>{u=void 0,s||e.onContinue?.()}),F=()=>{if(s)return;let w;const T=r===0?e.initialPromise:void 0;try{w=T??e.fn()}catch(j){w=Promise.reject(j)}Promise.resolve(w).then(y).catch(j=>{if(s)return;const L=e.retry??(wt?0:3),K=e.retryDelay??Pr,z=typeof K=="function"?K(r,j):K,C=L===!0||typeof L=="number"&&r<L||typeof L=="function"&&L(r,j);if(t||!C){Q(j);return}r++,e.onFail?.(r,j),Or(z).then(()=>o()?void 0:P()).then(()=>{t?Q(j):F()})})};return{promise:a,cancel:f,continue:()=>(u?.(),a),cancelRetry:d,continueRetry:h,canStart:p,start:()=>(p()?F():P().then(F),a)}}var Tr=e=>setTimeout(e,0);function Ar(){let e=[],t=0,r=d=>{d()},s=d=>{d()},u=Tr;const a=d=>{t?e.push(d):u(()=>{r(d)})},f=()=>{const d=e;e=[],d.length&&u(()=>{s(()=>{d.forEach(h=>{r(h)})})})};return{batch:d=>{let h;t++;try{h=d()}finally{t--,t||f()}return h},batchCalls:d=>(...h)=>{a(()=>{d(...h)})},schedule:a,setNotifyFunction:d=>{r=d},setBatchNotifyFunction:d=>{s=d},setScheduler:d=>{u=d}}}var x=Ar(),gt,$e,sr=($e=class{constructor(){v(this,gt)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),oe(this.gcTime)&&l(this,gt,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(wt?1/0:5*60*1e3))}clearGcTimeout(){n(this,gt)&&(clearTimeout(n(this,gt)),l(this,gt,void 0))}},gt=new WeakMap,$e),Mt,bt,B,Rt,U,Kt,Ot,W,rt,Ge,Fr=(Ge=class extends sr{constructor(t){super();v(this,W);v(this,Mt);v(this,bt);v(this,B);v(this,Rt);v(this,U);v(this,Kt);v(this,Ot);l(this,Ot,!1),l(this,Kt,t.defaultOptions),this.setOptions(t.options),this.observers=[],l(this,Rt,t.client),l(this,B,n(this,Rt).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,l(this,Mt,Dr(this.options)),this.state=t.state??n(this,Mt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return n(this,U)?.promise}setOptions(t){this.options={...n(this,Kt),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&n(this,B).remove(this)}setData(t,r){const s=ce(this.state.data,t,this.options);return O(this,W,rt).call(this,{data:s,type:"success",dataUpdatedAt:r?.updatedAt,manual:r?.manual}),s}setState(t,r){O(this,W,rt).call(this,{type:"setState",state:t,setStateOptions:r})}cancel(t){const r=n(this,U)?.promise;return n(this,U)?.cancel(t),r?r.then($).catch($):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(n(this,Mt))}isActive(){return this.observers.some(t=>V(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Re||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>vt(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Ve(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(r=>r.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),n(this,U)?.continue()}onOnline(){this.observers.find(r=>r.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),n(this,U)?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),n(this,B).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(r=>r!==t),this.observers.length||(n(this,U)&&(n(this,Ot)?n(this,U).cancel({revert:!0}):n(this,U).cancelRetry()),this.scheduleGc()),n(this,B).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||O(this,W,rt).call(this,{type:"invalidate"})}fetch(t,r){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&r?.cancelRefetch)this.cancel({silent:!0});else if(n(this,U))return n(this,U).continueRetry(),n(this,U).promise}if(t&&this.setOptions(t),!this.options.queryFn){const o=this.observers.find(p=>p.options.queryFn);o&&this.setOptions(o.options)}const s=new AbortController,u=o=>{Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(l(this,Ot,!0),s.signal)})},a=()=>{const o=Ze(this.options,r),y=(()=>{const Q={client:n(this,Rt),queryKey:this.queryKey,meta:this.meta};return u(Q),Q})();return l(this,Ot,!1),this.options.persister?this.options.persister(o,y,this):o(y)},d=(()=>{const o={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:n(this,Rt),state:this.state,fetchFn:a};return u(o),o})();this.options.behavior?.onFetch(d,this),l(this,bt,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==d.fetchOptions?.meta)&&O(this,W,rt).call(this,{type:"fetch",meta:d.fetchOptions?.meta});const h=o=>{ie(o)&&o.silent||O(this,W,rt).call(this,{type:"error",error:o}),ie(o)||(n(this,B).config.onError?.(o,this),n(this,B).config.onSettled?.(this.state.data,o,this)),this.scheduleGc()};return l(this,U,rr({initialPromise:r?.initialPromise,fn:d.fetchFn,abort:s.abort.bind(s),onSuccess:o=>{if(o===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(o)}catch(p){h(p);return}n(this,B).config.onSuccess?.(o,this),n(this,B).config.onSettled?.(o,this.state.error,this),this.scheduleGc()},onError:h,onFail:(o,p)=>{O(this,W,rt).call(this,{type:"failed",failureCount:o,error:p})},onPause:()=>{O(this,W,rt).call(this,{type:"pause"})},onContinue:()=>{O(this,W,rt).call(this,{type:"continue"})},retry:d.options.retry,retryDelay:d.options.retryDelay,networkMode:d.options.networkMode,canRun:()=>!0})),n(this,U).start()}},Mt=new WeakMap,bt=new WeakMap,B=new WeakMap,Rt=new WeakMap,U=new WeakMap,Kt=new WeakMap,Ot=new WeakMap,W=new WeakSet,rt=function(t){const r=s=>{switch(t.type){case"failed":return{...s,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...s,fetchStatus:"paused"};case"continue":return{...s,fetchStatus:"fetching"};case"fetch":return{...s,...nr(s.data,this.options),fetchMeta:t.meta??null};case"success":return l(this,bt,void 0),{...s,data:t.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const u=t.error;return ie(u)&&u.revert&&n(this,bt)?{...n(this,bt),fetchStatus:"idle"}:{...s,error:u,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,fetchFailureReason:u,fetchStatus:"idle",status:"error"};case"invalidate":return{...s,isInvalidated:!0};case"setState":return{...s,...t.state}}};this.state=r(this.state),x.batch(()=>{this.observers.forEach(s=>{s.onQueryUpdate()}),n(this,B).notify({query:this,type:"updated",action:t})})},Ge);function nr(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:tr(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Dr(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,r=t!==void 0,s=r?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var X,Ke,Mr=(Ke=class extends Wt{constructor(t={}){super();v(this,X);this.config=t,l(this,X,new Map)}build(t,r,s){const u=r.queryKey,a=r.queryHash??be(u,r);let f=this.get(a);return f||(f=new Fr({client:t,queryKey:u,queryHash:a,options:t.defaultQueryOptions(r),state:s,defaultOptions:t.getQueryDefaults(u)}),this.add(f)),f}add(t){n(this,X).has(t.queryHash)||(n(this,X).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const r=n(this,X).get(t.queryHash);r&&(t.destroy(),r===t&&n(this,X).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){x.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return n(this,X).get(t)}getAll(){return[...n(this,X).values()]}find(t){const r={exact:!0,...t};return this.getAll().find(s=>Me(r,s))}findAll(t={}){const r=this.getAll();return Object.keys(t).length>0?r.filter(s=>Me(t,s)):r}notify(t){x.batch(()=>{this.listeners.forEach(r=>{r(t)})})}onFocus(){x.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){x.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},X=new WeakMap,Ke),Z,k,Et,tt,ut,Ye,Qr=(Ye=class extends sr{constructor(t){super();v(this,tt);v(this,Z);v(this,k);v(this,Et);this.mutationId=t.mutationId,l(this,k,t.mutationCache),l(this,Z,[]),this.state=t.state||xr(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){n(this,Z).includes(t)||(n(this,Z).push(t),this.clearGcTimeout(),n(this,k).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){l(this,Z,n(this,Z).filter(r=>r!==t)),this.scheduleGc(),n(this,k).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){n(this,Z).length||(this.state.status==="pending"?this.scheduleGc():n(this,k).remove(this))}continue(){return n(this,Et)?.continue()??this.execute(this.state.variables)}async execute(t){const r=()=>{O(this,tt,ut).call(this,{type:"continue"})};l(this,Et,rr({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(a,f)=>{O(this,tt,ut).call(this,{type:"failed",failureCount:a,error:f})},onPause:()=>{O(this,tt,ut).call(this,{type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>n(this,k).canRun(this)}));const s=this.state.status==="pending",u=!n(this,Et).canStart();try{if(s)r();else{O(this,tt,ut).call(this,{type:"pending",variables:t,isPaused:u}),await n(this,k).config.onMutate?.(t,this);const f=await this.options.onMutate?.(t);f!==this.state.context&&O(this,tt,ut).call(this,{type:"pending",context:f,variables:t,isPaused:u})}const a=await n(this,Et).start();return await n(this,k).config.onSuccess?.(a,t,this.state.context,this),await this.options.onSuccess?.(a,t,this.state.context),await n(this,k).config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,t,this.state.context),O(this,tt,ut).call(this,{type:"success",data:a}),a}catch(a){try{throw await n(this,k).config.onError?.(a,t,this.state.context,this),await this.options.onError?.(a,t,this.state.context),await n(this,k).config.onSettled?.(void 0,a,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,a,t,this.state.context),a}finally{O(this,tt,ut).call(this,{type:"error",error:a})}}finally{n(this,k).runNext(this)}}},Z=new WeakMap,k=new WeakMap,Et=new WeakMap,tt=new WeakSet,ut=function(t){const r=s=>{switch(t.type){case"failed":return{...s,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...s,isPaused:!0};case"continue":return{...s,isPaused:!1};case"pending":return{...s,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...s,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...s,data:void 0,error:t.error,failureCount:s.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=r(this.state),x.batch(()=>{n(this,Z).forEach(s=>{s.onMutationUpdate(t)}),n(this,k).notify({mutation:this,type:"updated",action:t})})},Ye);function xr(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var st,J,Yt,Be,jr=(Be=class extends Wt{constructor(t={}){super();v(this,st);v(this,J);v(this,Yt);this.config=t,l(this,st,new Set),l(this,J,new Map),l(this,Yt,0)}build(t,r,s){const u=new Qr({mutationCache:this,mutationId:++Xt(this,Yt)._,options:t.defaultMutationOptions(r),state:s});return this.add(u),u}add(t){n(this,st).add(t);const r=Zt(t);if(typeof r=="string"){const s=n(this,J).get(r);s?s.push(t):n(this,J).set(r,[t])}this.notify({type:"added",mutation:t})}remove(t){if(n(this,st).delete(t)){const r=Zt(t);if(typeof r=="string"){const s=n(this,J).get(r);if(s)if(s.length>1){const u=s.indexOf(t);u!==-1&&s.splice(u,1)}else s[0]===t&&n(this,J).delete(r)}}this.notify({type:"removed",mutation:t})}canRun(t){const r=Zt(t);if(typeof r=="string"){const u=n(this,J).get(r)?.find(a=>a.state.status==="pending");return!u||u===t}else return!0}runNext(t){const r=Zt(t);return typeof r=="string"?n(this,J).get(r)?.find(u=>u!==t&&u.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){x.batch(()=>{n(this,st).forEach(t=>{this.notify({type:"removed",mutation:t})}),n(this,st).clear(),n(this,J).clear()})}getAll(){return Array.from(n(this,st))}find(t){const r={exact:!0,...t};return this.getAll().find(s=>Qe(r,s))}findAll(t={}){return this.getAll().filter(r=>Qe(t,r))}notify(t){x.batch(()=>{this.listeners.forEach(r=>{r(t)})})}resumePausedMutations(){const t=this.getAll().filter(r=>r.state.isPaused);return x.batch(()=>Promise.all(t.map(r=>r.continue().catch($))))}},st=new WeakMap,J=new WeakMap,Yt=new WeakMap,Be);function Zt(e){return e.options.scope?.id}function Ie(e){return{onFetch:(t,r)=>{const s=t.options,u=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],f=t.state.data?.pageParams||[];let d={pages:[],pageParams:[]},h=0;const o=async()=>{let p=!1;const y=F=>{Object.defineProperty(F,"signal",{enumerable:!0,get:()=>(t.signal.aborted?p=!0:t.signal.addEventListener("abort",()=>{p=!0}),t.signal)})},Q=Ze(t.options,t.fetchOptions),P=async(F,w,T)=>{if(p)return Promise.reject();if(w==null&&F.pages.length)return Promise.resolve(F);const L=(()=>{const nt={client:t.client,queryKey:t.queryKey,pageParam:w,direction:T?"backward":"forward",meta:t.options.meta};return y(nt),nt})(),K=await Q(L),{maxPages:z}=t.options,C=T?Cr:Er;return{pages:C(F.pages,K,z),pageParams:C(F.pageParams,w,z)}};if(u&&a.length){const F=u==="backward",w=F?Ir:qe,T={pages:a,pageParams:f},j=w(s,T);d=await P(T,j,F)}else{const F=e??a.length;do{const w=h===0?f[0]??s.initialPageParam:qe(s,d);if(h>0&&w==null)break;d=await P(d,w),h++}while(h<F)}return d};t.options.persister?t.fetchFn=()=>t.options.persister?.(o,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=o}}}function qe(e,{pages:t,pageParams:r}){const s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}function Ir(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}var A,ht,lt,Qt,xt,ft,jt,It,ze,ss=(ze=class{constructor(e={}){v(this,A);v(this,ht);v(this,lt);v(this,Qt);v(this,xt);v(this,ft);v(this,jt);v(this,It);l(this,A,e.queryCache||new Mr),l(this,ht,e.mutationCache||new jr),l(this,lt,e.defaultOptions||{}),l(this,Qt,new Map),l(this,xt,new Map),l(this,ft,0)}mount(){Xt(this,ft)._++,n(this,ft)===1&&(l(this,jt,Oe.subscribe(async e=>{e&&(await this.resumePausedMutations(),n(this,A).onFocus())})),l(this,It,te.subscribe(async e=>{e&&(await this.resumePausedMutations(),n(this,A).onOnline())})))}unmount(){var e,t;Xt(this,ft)._--,n(this,ft)===0&&((e=n(this,jt))==null||e.call(this),l(this,jt,void 0),(t=n(this,It))==null||t.call(this),l(this,It,void 0))}isFetching(e){return n(this,A).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return n(this,ht).findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return n(this,A).get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=n(this,A).build(this,t),s=r.state.data;return s===void 0?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(vt(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return n(this,A).findAll(e).map(({queryKey:t,state:r})=>{const s=r.data;return[t,s]})}setQueryData(e,t,r){const s=this.defaultQueryOptions({queryKey:e}),a=n(this,A).get(s.queryHash)?.state.data,f=Rr(t,a);if(f!==void 0)return n(this,A).build(this,s).setData(f,{...r,manual:!0})}setQueriesData(e,t,r){return x.batch(()=>n(this,A).findAll(e).map(({queryKey:s})=>[s,this.setQueryData(s,t,r)]))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return n(this,A).get(t.queryHash)?.state}removeQueries(e){const t=n(this,A);x.batch(()=>{t.findAll(e).forEach(r=>{t.remove(r)})})}resetQueries(e,t){const r=n(this,A);return x.batch(()=>(r.findAll(e).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const r={revert:!0,...t},s=x.batch(()=>n(this,A).findAll(e).map(u=>u.cancel(r)));return Promise.all(s).then($).catch($)}invalidateQueries(e,t={}){return x.batch(()=>(n(this,A).findAll(e).forEach(r=>{r.invalidate()}),e?.refetchType==="none"?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t)))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},s=x.batch(()=>n(this,A).findAll(e).filter(u=>!u.isDisabled()&&!u.isStatic()).map(u=>{let a=u.fetch(void 0,r);return r.throwOnError||(a=a.catch($)),u.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(s).then($)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const r=n(this,A).build(this,t);return r.isStaleByTime(vt(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then($).catch($)}fetchInfiniteQuery(e){return e.behavior=Ie(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then($).catch($)}ensureInfiniteQueryData(e){return e.behavior=Ie(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return te.isOnline()?n(this,ht).resumePausedMutations():Promise.resolve()}getQueryCache(){return n(this,A)}getMutationCache(){return n(this,ht)}getDefaultOptions(){return n(this,lt)}setDefaultOptions(e){l(this,lt,e)}setQueryDefaults(e,t){n(this,Qt).set($t(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...n(this,Qt).values()],r={};return t.forEach(s=>{Gt(e,s.queryKey)&&Object.assign(r,s.defaultOptions)}),r}setMutationDefaults(e,t){n(this,xt).set($t(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...n(this,xt).values()],r={};return t.forEach(s=>{Gt(e,s.mutationKey)&&Object.assign(r,s.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...n(this,lt).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=be(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Re&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...n(this,lt).mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){n(this,A).clear(),n(this,ht).clear()}},A=new WeakMap,ht=new WeakMap,lt=new WeakMap,Qt=new WeakMap,xt=new WeakMap,ft=new WeakMap,jt=new WeakMap,It=new WeakMap,ze),N,E,Bt,H,Ct,qt,dt,pt,zt,Ut,Lt,_t,St,yt,kt,S,Nt,le,fe,de,pe,ye,ve,me,ir,We,qr=(We=class extends Wt{constructor(t,r){super();v(this,S);v(this,N);v(this,E);v(this,Bt);v(this,H);v(this,Ct);v(this,qt);v(this,dt);v(this,pt);v(this,zt);v(this,Ut);v(this,Lt);v(this,_t);v(this,St);v(this,yt);v(this,kt,new Set);this.options=r,l(this,N,t),l(this,pt,null),l(this,dt,he()),this.options.experimental_prefetchInRender||n(this,dt).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(r)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(n(this,E).addObserver(this),Ue(n(this,E),this.options)?O(this,S,Nt).call(this):this.updateResult(),O(this,S,pe).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ge(n(this,E),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ge(n(this,E),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,O(this,S,ye).call(this),O(this,S,ve).call(this),n(this,E).removeObserver(this)}setOptions(t){const r=this.options,s=n(this,E);if(this.options=n(this,N).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof V(this.options.enabled,n(this,E))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");O(this,S,me).call(this),n(this,E).setOptions(this.options),r._defaulted&&!ue(this.options,r)&&n(this,N).getQueryCache().notify({type:"observerOptionsUpdated",query:n(this,E),observer:this});const u=this.hasListeners();u&&Le(n(this,E),s,this.options,r)&&O(this,S,Nt).call(this),this.updateResult(),u&&(n(this,E)!==s||V(this.options.enabled,n(this,E))!==V(r.enabled,n(this,E))||vt(this.options.staleTime,n(this,E))!==vt(r.staleTime,n(this,E)))&&O(this,S,le).call(this);const a=O(this,S,fe).call(this);u&&(n(this,E)!==s||V(this.options.enabled,n(this,E))!==V(r.enabled,n(this,E))||a!==n(this,yt))&&O(this,S,de).call(this,a)}getOptimisticResult(t){const r=n(this,N).getQueryCache().build(n(this,N),t),s=this.createResult(r,t);return Lr(this,s)&&(l(this,H,s),l(this,qt,this.options),l(this,Ct,n(this,E).state)),s}getCurrentResult(){return n(this,H)}trackResult(t,r){return new Proxy(t,{get:(s,u)=>(this.trackProp(u),r?.(u),Reflect.get(s,u))})}trackProp(t){n(this,kt).add(t)}getCurrentQuery(){return n(this,E)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const r=n(this,N).defaultQueryOptions(t),s=n(this,N).getQueryCache().build(n(this,N),r);return s.fetch().then(()=>this.createResult(s,r))}fetch(t){return O(this,S,Nt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),n(this,H)))}createResult(t,r){const s=n(this,E),u=this.options,a=n(this,H),f=n(this,Ct),d=n(this,qt),o=t!==s?t.state:n(this,Bt),{state:p}=t;let y={...p},Q=!1,P;if(r._optimisticResults){const I=this.hasListeners(),Pt=!I&&Ue(t,r),et=I&&Le(t,s,r,u);(Pt||et)&&(y={...y,...nr(p.data,t.options)}),r._optimisticResults==="isRestoring"&&(y.fetchStatus="idle")}let{error:F,errorUpdatedAt:w,status:T}=y;P=y.data;let j=!1;if(r.placeholderData!==void 0&&P===void 0&&T==="pending"){let I;a?.isPlaceholderData&&r.placeholderData===d?.placeholderData?(I=a.data,j=!0):I=typeof r.placeholderData=="function"?r.placeholderData(n(this,Lt)?.state.data,n(this,Lt)):r.placeholderData,I!==void 0&&(T="success",P=ce(a?.data,I,r),Q=!0)}if(r.select&&P!==void 0&&!j)if(a&&P===f?.data&&r.select===n(this,zt))P=n(this,Ut);else try{l(this,zt,r.select),P=r.select(P),P=ce(a?.data,P,r),l(this,Ut,P),l(this,pt,null)}catch(I){l(this,pt,I)}n(this,pt)&&(F=n(this,pt),P=n(this,Ut),w=Date.now(),T="error");const L=y.fetchStatus==="fetching",K=T==="pending",z=T==="error",C=K&&L,nt=P!==void 0,Y={status:T,fetchStatus:y.fetchStatus,isPending:K,isSuccess:T==="success",isError:z,isInitialLoading:C,isLoading:C,data:P,dataUpdatedAt:y.dataUpdatedAt,error:F,errorUpdatedAt:w,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>o.dataUpdateCount||y.errorUpdateCount>o.errorUpdateCount,isFetching:L,isRefetching:L&&!K,isLoadingError:z&&!nt,isPaused:y.fetchStatus==="paused",isPlaceholderData:Q,isRefetchError:z&&nt,isStale:Ee(t,r),refetch:this.refetch,promise:n(this,dt)};if(this.options.experimental_prefetchInRender){const I=it=>{Y.status==="error"?it.reject(Y.error):Y.data!==void 0&&it.resolve(Y.data)},Pt=()=>{const it=l(this,dt,Y.promise=he());I(it)},et=n(this,dt);switch(et.status){case"pending":t.queryHash===s.queryHash&&I(et);break;case"fulfilled":(Y.status==="error"||Y.data!==et.value)&&Pt();break;case"rejected":(Y.status!=="error"||Y.error!==et.reason)&&Pt();break}}return Y}updateResult(){const t=n(this,H),r=this.createResult(n(this,E),this.options);if(l(this,Ct,n(this,E).state),l(this,qt,this.options),n(this,Ct).data!==void 0&&l(this,Lt,n(this,E)),ue(r,t))return;l(this,H,r);const s=()=>{if(!t)return!0;const{notifyOnChangeProps:u}=this.options,a=typeof u=="function"?u():u;if(a==="all"||!a&&!n(this,kt).size)return!0;const f=new Set(a??n(this,kt));return this.options.throwOnError&&f.add("error"),Object.keys(n(this,H)).some(d=>{const h=d;return n(this,H)[h]!==t[h]&&f.has(h)})};O(this,S,ir).call(this,{listeners:s()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&O(this,S,pe).call(this)}},N=new WeakMap,E=new WeakMap,Bt=new WeakMap,H=new WeakMap,Ct=new WeakMap,qt=new WeakMap,dt=new WeakMap,pt=new WeakMap,zt=new WeakMap,Ut=new WeakMap,Lt=new WeakMap,_t=new WeakMap,St=new WeakMap,yt=new WeakMap,kt=new WeakMap,S=new WeakSet,Nt=function(t){O(this,S,me).call(this);let r=n(this,E).fetch(this.options,t);return t?.throwOnError||(r=r.catch($)),r},le=function(){O(this,S,ye).call(this);const t=vt(this.options.staleTime,n(this,E));if(wt||n(this,H).isStale||!oe(t))return;const s=Ve(n(this,H).dataUpdatedAt,t)+1;l(this,_t,setTimeout(()=>{n(this,H).isStale||this.updateResult()},s))},fe=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(n(this,E)):this.options.refetchInterval)??!1},de=function(t){O(this,S,ve).call(this),l(this,yt,t),!(wt||V(this.options.enabled,n(this,E))===!1||!oe(n(this,yt))||n(this,yt)===0)&&l(this,St,setInterval(()=>{(this.options.refetchIntervalInBackground||Oe.isFocused())&&O(this,S,Nt).call(this)},n(this,yt)))},pe=function(){O(this,S,le).call(this),O(this,S,de).call(this,O(this,S,fe).call(this))},ye=function(){n(this,_t)&&(clearTimeout(n(this,_t)),l(this,_t,void 0))},ve=function(){n(this,St)&&(clearInterval(n(this,St)),l(this,St,void 0))},me=function(){const t=n(this,N).getQueryCache().build(n(this,N),this.options);if(t===n(this,E))return;const r=n(this,E);l(this,E,t),l(this,Bt,t.state),this.hasListeners()&&(r?.removeObserver(this),t.addObserver(this))},ir=function(t){x.batch(()=>{t.listeners&&this.listeners.forEach(r=>{r(n(this,H))}),n(this,N).getQueryCache().notify({query:n(this,E),type:"observerResultsUpdated"})})},We);function Ur(e,t){return V(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Ue(e,t){return Ur(e,t)||e.state.data!==void 0&&ge(e,t,t.refetchOnMount)}function ge(e,t,r){if(V(t.enabled,e)!==!1&&vt(t.staleTime,e)!=="static"){const s=typeof r=="function"?r(e):r;return s==="always"||s!==!1&&Ee(e,t)}return!1}function Le(e,t,r,s){return(e!==t||V(s.enabled,e)===!1)&&(!r.suspense||e.state.status!=="error")&&Ee(e,r)}function Ee(e,t){return V(t.enabled,e)!==!1&&e.isStaleByTime(vt(t.staleTime,e))}function Lr(e,t){return!ue(e.getCurrentResult(),t)}var or=G.createContext(void 0),kr=e=>{const t=G.useContext(or);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},ns=({client:e,children:t})=>(G.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),vr.jsx(or.Provider,{value:e,children:t})),ur=G.createContext(!1),Hr=()=>G.useContext(ur);ur.Provider;function Nr(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var $r=G.createContext(Nr()),Gr=()=>G.useContext($r),Kr=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Yr=e=>{G.useEffect(()=>{e.clearReset()},[e])},Br=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:u})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(u&&e.data===void 0||_r(r,[e.error,s])),zr=e=>{if(e.suspense){const t=s=>s==="static"?s:Math.max(s??1e3,1e3),r=e.staleTime;e.staleTime=typeof r=="function"?(...s)=>t(r(...s)):t(r),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Wr=(e,t)=>e.isLoading&&e.isFetching&&!t,Jr=(e,t)=>e?.suspense&&t.isPending,ke=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function Vr(e,t,r){const s=Hr(),u=Gr(),a=kr(),f=a.defaultQueryOptions(e);a.getDefaultOptions().queries?._experimental_beforeQuery?.(f),f._optimisticResults=s?"isRestoring":"optimistic",zr(f),Kr(f,u),Yr(u);const d=!a.getQueryCache().get(f.queryHash),[h]=G.useState(()=>new t(a,f)),o=h.getOptimisticResult(f),p=!s&&e.subscribed!==!1;if(G.useSyncExternalStore(G.useCallback(y=>{const Q=p?h.subscribe(x.batchCalls(y)):$;return h.updateResult(),Q},[h,p]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),G.useEffect(()=>{h.setOptions(f)},[f,h]),Jr(f,o))throw ke(f,h,u);if(Br({result:o,errorResetBoundary:u,throwOnError:f.throwOnError,query:a.getQueryCache().get(f.queryHash),suspense:f.suspense}))throw o.error;return a.getDefaultOptions().queries?._experimental_afterQuery?.(f,o),f.experimental_prefetchInRender&&!wt&&Wr(o,s)&&(d?ke(f,h,u):a.getQueryCache().get(f.queryHash)?.promise)?.catch($).finally(()=>{h.updateResult()}),f.notifyOnChangeProps?o:h.trackResult(o)}function is(e,t){return Vr(e,qr)}export{ns as Q,gr as R,G as a,es as b,Je as c,ts as d,Zr as e,ss as f,dr as g,yr as h,vr as j,rs as r,is as u};
//# sourceMappingURL=vendor-B032F4SZ.js.map
