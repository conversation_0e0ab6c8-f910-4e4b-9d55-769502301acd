using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Vessel;

[Authorize]
public class BoundedZoneAppService :
    CrudAppService<ZoneDetail, ZoneDetailDto, Guid, PagedAndSortedResultRequestDto, ZoneDetailCreateUpdateDto, ZoneDetailCreateUpdateDto>,
    IZoneDetailAppService
{
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ZoneDetailMapper _mapper;
    private readonly ILogger<BoundedZoneAppService> _logger;

    public BoundedZoneAppService(
        IZoneDetailRepository zoneDetailRepository,
        ZoneDetailMapper mapper,
        ILogger<BoundedZoneAppService> logger)
        : base(zoneDetailRepository)
    {
        _zoneDetailRepository = zoneDetailRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ZoneDetailDto> CreateAsync(ZoneDetailCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        // Set audit properties
        entity.CreatedAt = Clock.Now;

        await _zoneDetailRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ZoneDetailDto> UpdateAsync(Guid id, ZoneDetailCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _zoneDetailRepository.GetAsync(id);

        // Preserve original creation info
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        var originalCreatedId = entity.CreatedId;

        _mapper.MapToEntity(input, entity);

        // Restore preserved values
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.CreatedId = originalCreatedId;

        // Set update audit properties
        entity.UpdatedAt = Clock.Now;

        await _zoneDetailRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _zoneDetailRepository.GetAsync(id);

        // Soft delete implementation
        entity.DeletedAt = Clock.Now;
        entity.DeleteBy = CurrentUser.UserName;
        entity.Deleted = "Y";

        await _zoneDetailRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<ZoneDetailDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _zoneDetailRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ZoneDetailDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        // Use optimized queryable that pre-filters deleted records
        var queryable = await _zoneDetailRepository.GetOptimizedQueryableAsync();

        // Apply sorting before counting for better performance
        var sortedQueryable = queryable
            .OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(ZoneDetail.DocEntry) : input.Sorting);

        // Use optimized counting strategy with timeout protection
        int totalCount;
        try
        {
            // Try to get count with timeout protection
            totalCount = await _zoneDetailRepository.GetCountWithTimeoutAsync(30); // 30 second timeout
        }
        catch (TimeoutException ex)
        {
            _logger.LogWarning("Count query timed out: {Message}", ex.Message);

            // Fallback strategies for large datasets
            if (input.SkipCount > 10000) // If skipping more than 10k records, don't count
            {
                totalCount = -1; // Indicate unknown count
                _logger.LogInformation("Skipping count for large dataset (skip count: {SkipCount})", input.SkipCount);
            }
            else
            {
                // Use a more efficient count with limit
                totalCount = await AsyncExecuter.CountAsync(
                    queryable.Take(100000) // Limit count to prevent timeout
                );
                _logger.LogInformation("Used limited count strategy, result: {Count}", totalCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during count operation");
            throw;
        }

        // Apply paging
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable
                .PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ZoneDetailDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<ZoneDetailDto>> FilterListAsync(QueryParametersDto parameters)
    {
        // Get base query from repository
        var query = await _zoneDetailRepository.GetQueryableWithIncludesAsync();

        // Apply dynamic filtering and sorting HERE (in Application Service)
        query = ApplyDynamicQuery(query, parameters);

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply paging
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        // Map to DTOs using Mapperly
        var dtos = _mapper.MapToDtoList(items);

        return new PagedResultDto<ZoneDetailDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<ZoneDetail> ApplyDynamicQuery(IQueryable<ZoneDetail> query, QueryParametersDto parameters)
    {
        // Check if we need to include deeper relationships
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ZoneDetail>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ZoneDetail>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }
}