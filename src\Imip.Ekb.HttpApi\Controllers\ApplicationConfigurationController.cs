using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Controllers;

/// <summary>
/// Proxy controller that redirects application configuration requests to the identity server
/// </summary>
[Route("api/abp")]
[ApiController]
public class ApplicationConfigurationController : AbpControllerBase
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ApplicationConfigurationController> _logger;

    public ApplicationConfigurationController(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ApplicationConfigurationController> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Proxy the application configuration request to the identity server
    /// </summary>
    /// <param name="includeLocalizationResources">Whether to include localization resources</param>
    /// <returns>Application configuration from identity server</returns>
    [HttpGet("application-configuration")]
    [Authorize] // Require authentication
    public async Task<IActionResult> GetApplicationConfigurationAsync(
        [FromQuery] bool includeLocalizationResources = true)
    {
        try
        {
            // Get the access token from the current request
            var accessToken = await HttpContext.GetTokenAsync("access_token");

            // If not found in authentication properties, try Authorization header
            if (string.IsNullOrEmpty(accessToken))
            {
                var authHeader = HttpContext.Request.Headers.Authorization.FirstOrDefault();
                if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                {
                    accessToken = authHeader["Bearer ".Length..].Trim();
                }
            }

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No access token found for application configuration request");
                return Unauthorized(new { error = "Access token required" });
            }

            var identityServerUrl = _configuration["AuthServer:Authority"];
            if (string.IsNullOrEmpty(identityServerUrl))
            {
                _logger.LogError("AuthServer:Authority not configured");
                return StatusCode(500, new { error = "Identity server not configured" });
            }

            var endpoint = $"{identityServerUrl}/api/abp/application-configuration?includeLocalizationResources={includeLocalizationResources}";

            _logger.LogDebug("Proxying application configuration request to: {Endpoint}", endpoint);

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            client.Timeout = TimeSpan.FromSeconds(30);

            var response = await client.GetAsync(endpoint);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";

                _logger.LogDebug("Successfully retrieved application configuration from identity server");

                return Content(content, contentType);
            }
            else
            {
                _logger.LogWarning("Identity server returned status code {StatusCode} for application configuration request",
                    response.StatusCode);

                var errorContent = await response.Content.ReadAsStringAsync();
                return StatusCode((int)response.StatusCode, new
                {
                    error = "Failed to retrieve application configuration from identity server",
                    details = errorContent,
                    statusCode = response.StatusCode
                });
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while requesting application configuration from identity server");
            return StatusCode(502, new
            {
                error = "Failed to connect to identity server",
                details = ex.Message
            });
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while requesting application configuration from identity server");
            return StatusCode(504, new
            {
                error = "Timeout connecting to identity server",
                details = "The request to the identity server timed out"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while requesting application configuration from identity server");
            return StatusCode(500, new
            {
                error = "Internal server error",
                details = ex.Message
            });
        }
    }
}
