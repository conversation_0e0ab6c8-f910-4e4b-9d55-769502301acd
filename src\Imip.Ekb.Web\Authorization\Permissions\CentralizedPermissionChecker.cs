using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.DependencyInjection;

namespace Imip.Ekb.Web.Authorization.Permissions;

[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IPermissionChecker))]
public class CentralizedPermissionChecker : IPermissionChecker, ITransientDependency
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CentralizedPermissionChecker> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IWebHostEnvironment _environment;
    private readonly ApplicationConfigurationService _applicationConfigurationService;

    // Shared JsonSerializerOptions instance
    private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true
    };

    public CentralizedPermissionChecker(
        IHttpContextAccessor httpContextAccessor,
        IConfiguration configuration,
        ILogger<CentralizedPermissionChecker> logger,
        IHttpClientFactory httpClientFactory,
        IWebHostEnvironment environment,
        ApplicationConfigurationService applicationConfigurationService)
    {
        _httpContextAccessor = httpContextAccessor;
        _configuration = configuration;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _environment = environment;
        _applicationConfigurationService = applicationConfigurationService;
    }

    // Implementation for IsGrantedAsync(string name)
    public async Task<bool> IsGrantedAsync(string name)
    {
        return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, name);
    }

    // Implementation for IsGrantedAsync(ClaimsPrincipal? principal, string name)
    public async Task<bool> IsGrantedAsync(ClaimsPrincipal? principal, string name)
    {
        try
        {
            if (principal == null)
            {
                // _logger.LogWarning("Principal is null when checking permission: {Permission}", name);
                return false;
            }

            if (!principal.Identity?.IsAuthenticated ?? true)
            {
                // _logger.LogDebug("User is not authenticated when checking permission: {Permission}", name);
                return false;
            }

            // Special case: Allow access to Quartz endpoints for authenticated users
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null && httpContext.Request.Path.StartsWithSegments("/quartz"))
            {
                _logger.LogDebug("Allowing access to Quartz endpoint: {Path} for authenticated user", httpContext.Request.Path);
                return true;
            }

            // Log the permission being checked
            // _logger.LogDebug("Checking permission: {Permission} for user: {UserId}", name,
            //     principal.FindFirstValue(ClaimTypes.NameIdentifier) ?? "unknown");

            // Get all roles the user has
            var userRoles = principal.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            // Get the access token
            var identityServerToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(identityServerToken))
            {
                // _logger.LogWarning("Access token is null or empty when checking permission: {Permission}", name);
                return false;
            }

            // Log token information (only length for security)
            // _logger.LogDebug("Using token for permission check: {TokenLength} characters",
            //     identityServerToken.Length);

            // Check if the permission is granted in the application configuration
            var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(identityServerToken);

            // Log all granted policies for debugging
            _logger.LogWarning("All granted policies: {Policies}",
                string.Join(", ", grantedPolicies.Where(p => p.Value).Select(p => p.Key)));

            // Try with the original permission name
            if (grantedPolicies.TryGetValue(name, out var isGranted) && isGranted)
            {
                // _logger.LogDebug("Permission {Permission} is granted in application configuration", name);
                return true;
            }

            // Try with the "IdentityServer." prefix if the permission doesn't have it
            if (!name.StartsWith("IdentityServer."))
            {
                var identityServerPermissionName = "IdentityServer." + name;
                if (grantedPolicies.TryGetValue(identityServerPermissionName, out isGranted) && isGranted)
                {
                    // _logger.LogDebug(
                    //     "Permission {Permission} is granted in application configuration as {IdentityServerPermissionName}",
                    //     name, identityServerPermissionName);
                    return true;
                }
            }

            // Check for parent permission (e.g., if WismaApp.Room.View is not found, check WismaApp.Room)
            var lastDotIndex = name.LastIndexOf('.');
            if (lastDotIndex > 0)
            {
                var parentPermission = name[..lastDotIndex]; // Using range operator instead of Substring
                if (grantedPolicies.TryGetValue(parentPermission, out isGranted) && isGranted)
                {
                    // _logger.LogDebug("Parent permission {ParentPermission} is granted for {Permission}",
                    //     parentPermission, name);
                    return true;
                }
            }

            // We no longer use role-based permission checking
            // All permissions are checked directly with the Identity Server API

            // _logger.LogDebug(
            //     "Permission {Permission} not found in application configuration, checking with Identity Server API",
            //     name);

            // If the permission is not found in the application configuration, fall back to checking with the Identity Server API
            isGranted = await CheckPermissionWithIdentityServerAsync(name, identityServerToken);

            if (isGranted)
            {
                // _logger.LogDebug("Permission {Permission} is granted by Identity Server API", name);
                return true;
            }
            else
            {
                // _logger.LogWarning("Permission {Permission} is denied by Identity Server API", name);
                return false;
            }
        }
        catch (Exception ex)
        {
            // _logger.LogError(ex, "Error checking permission {Permission}", name);
            return false;
        }
    }

    // Implementation for IsGrantedAsync(Guid userId, string name)
    public async Task<bool> IsGrantedAsync(Guid userId, string name)
    {
        try
        {
            // Log permission check with appropriate level based on environment
            if (_environment.IsDevelopment())
            {
                _logger.LogDebug("Checking permission for user {UserId}: {Permission}", userId, name);
            }

            // Get the access token
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                // _logger.LogWarning(
                //     "Access token is null or empty when checking permission for user: {UserId}, {Permission}", userId,
                //     name);
                return false;
            }

            // Call Identity Server to check permission for specific user
            var identityServerUrl = _configuration["AuthServer:Authority"];
            var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check/user";

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var response =
                await client.GetAsync($"{permissionCheckEndpoint}?userId={userId}&name={Uri.EscapeDataString(name)}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<PermissionCheckResult>(content, _jsonOptions);
                return result?.IsGranted ?? false;
            }

            // _logger.LogWarning("Failed to check permission with Identity Server for user {UserId}. Status: {Status}",
            //     userId, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for user ID {UserId}, {Permission}", userId, name);
            return false;
        }
    }

    // Implementation for IsGrantedAsync(string[] names)
    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(string[] names)
    {
        return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, names);
    }

    // Implementation for IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
    {
        var result = new MultiplePermissionGrantResult();

        if (names == null || names.Length == 0)
        {
            return result;
        }

        try
        {
            // Log with appropriate level based on environment
            if (_environment.IsDevelopment())
            {
                _logger.LogDebug("Checking permissions: {Permissions}", string.Join(", ", names));
            }

            // If we have a principal, try to check permissions directly
            if (principal != null && principal.Identity?.IsAuthenticated == true)
            {
                // Get the access token
                var accessToken = await GetAccessTokenAsync();
                if (!string.IsNullOrEmpty(accessToken))
                {
                    // Call Identity Server to check multiple permissions at once
                    var identityServerUrl = _configuration["AuthServer:Authority"];
                    var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check/multiple";

                    // _logger.LogDebug("Checking multiple permissions with Identity Server");

                    var client = _httpClientFactory.CreateClient("IdentityServer");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    // Build query string with multiple name parameters
                    var queryString = string.Join("&", names.Select(n => $"names={Uri.EscapeDataString(n)}"));
                    var requestUrl = $"{permissionCheckEndpoint}?{queryString}";

                    var response = await client.GetAsync(requestUrl);
                    // _logger.LogDebug("Response status code: {StatusCode}", response.StatusCode);

                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        var apiResult =
                            JsonSerializer.Deserialize<MultiplePermissionCheckResult>(content, _jsonOptions);

                        if (apiResult?.Permissions != null)
                        {
                            foreach (var permission in apiResult.Permissions)
                            {
                                var grantResult = permission.Value
                                    ? PermissionGrantResult.Granted
                                    : PermissionGrantResult.Prohibited;

                                result.Result[permission.Key] = grantResult;
                            }

                            return result;
                        }
                        else
                        {
                            _logger.LogWarning("No permissions returned from Identity Server");
                        }
                    }

                    // _logger.LogWarning("Failed to check multiple permissions with Identity Server. Status: {Status}",
                    //     response.StatusCode);

                    // Try to read the error response
                    try
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        // _logger.LogWarning("Error response from Identity Server: {ErrorContent}", errorContent);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("Could not read error response: {ErrorMessage}", ex.Message);
                    }
                }
            }

            // Fallback: check permissions one by one
            foreach (var name in names)
            {
                result.Result[name] = await IsGrantedAsync(principal, name)
                    ? PermissionGrantResult.Granted
                    : PermissionGrantResult.Prohibited;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking multiple permissions");

            // In case of error, mark all permissions as prohibited
            foreach (var name in names)
            {
                result.Result[name] = PermissionGrantResult.Prohibited;
            }

            return result;
        }
    }

    private async Task<string?> GetAccessTokenAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext == null)
        {
            return null;
        }

        // First try to get the token from the optimized middleware context items
        if (httpContext.Items.TryGetValue("AccessToken", out var tokenFromContext) &&
            tokenFromContext is string contextToken && !string.IsNullOrEmpty(contextToken))
        {
            return contextToken;
        }

        // Then try to get the token from the authentication properties
        var accessToken = await httpContext.GetTokenAsync("access_token");

        // If the token is not found in the authentication properties, try to extract it from the Authorization header
        if (string.IsNullOrEmpty(accessToken))
        {
            var authHeader = httpContext.Request.Headers.Authorization.FirstOrDefault();
            if (!string.IsNullOrEmpty(authHeader) &&
                authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                accessToken = authHeader["Bearer ".Length..].Trim();
                // _logger.LogDebug("Extracted access token from Authorization header");
            }
        }

        return accessToken;
    }

    private async Task<bool> CheckPermissionWithIdentityServerAsync(string permissionName, string accessToken)
    {
        try
        {
            var identityServerUrl = _configuration["AuthServer:Authority"];
            var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check";

            // _logger.LogDebug("Checking permission {Permission} with Identity Server", permissionName);

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            // Set a reasonable timeout
            client.Timeout = TimeSpan.FromSeconds(30);

            var requestUrl = $"{permissionCheckEndpoint}?name={Uri.EscapeDataString(permissionName)}";

            // Add fallback DNS resolution for known hosts
            var knownHosts = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { "api-identity-dev.imip.co.id", "**********" },
                { "api-identity.imip.co.id", "**********" }
            };

            // Extract hostname from URL
            if (!string.IsNullOrEmpty(identityServerUrl))
            {
                try
                {
                    var uri = new Uri(identityServerUrl);
                    var hostname = uri.Host;

                    // Check if we need to use a fallback IP
                    if (knownHosts.TryGetValue(hostname, out var fallbackIp))
                    {
                        // _logger.LogDebug("Using fallback IP {FallbackIp} for hostname {Hostname}", fallbackIp,
                        //     hostname);

                        // Create a modified URL with the IP address instead of the hostname
                        var scheme = uri.Scheme;
                        var port = uri.Port > 0 && uri.Port != 80 && uri.Port != 443 ? $":{uri.Port}" : "";
                        var path = uri.AbsolutePath.TrimEnd('/');

                        var fallbackPermissionCheckEndpoint =
                            $"{scheme}://{fallbackIp}{port}{path}/api/permission-check";
                        var fallbackRequestUrl =
                            $"{fallbackPermissionCheckEndpoint}?name={Uri.EscapeDataString(permissionName)}";

                        try
                        {
                            // Try with the fallback URL first
                            using var fallbackCts =
                                new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(10));
                            var fallbackResponse = await client.GetAsync(fallbackRequestUrl, fallbackCts.Token);

                            if (fallbackResponse.IsSuccessStatusCode)
                            {
                                // _logger.LogDebug("Successfully used fallback URL");

                                var content = await fallbackResponse.Content.ReadAsStringAsync();

                                // Use the shared JsonSerializerOptions instance
                                var result = JsonSerializer.Deserialize<PermissionCheckResult>(content, _jsonOptions);

                                if (result?.IsGranted == true)
                                {
                                    // _logger.LogDebug("Permission {Permission} is granted by Identity Server",
                                    //     permissionName);
                                    return true;
                                }
                                else
                                {
                                    // _logger.LogWarning("Permission {Permission} is denied by Identity Server",
                                    //     permissionName);
                                    return false;
                                }
                            }

                            // _logger.LogDebug("Fallback URL failed with status code {StatusCode}, trying original URL",
                            //     fallbackResponse.StatusCode);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug(ex, "Error using fallback URL, trying original URL");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error parsing Identity Server URL, trying original URL");
                }
            }

            // Use a timeout for the request
            using var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(10));
            var response = await client.GetAsync(requestUrl, cts.Token);

            // _logger.LogDebug("Response status code: {StatusCode}", response.StatusCode);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();

                // Use the shared JsonSerializerOptions instance
                var result = JsonSerializer.Deserialize<PermissionCheckResult>(content, _jsonOptions);

                if (result?.IsGranted == true)
                {
                    // _logger.LogDebug("Permission {Permission} is granted by Identity Server", permissionName);
                    return true;
                }
                else
                {
                    // _logger.LogWarning("Permission {Permission} is denied by Identity Server", permissionName);
                    return false;
                }
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                     response.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                // _logger.LogWarning(
                //     "Authentication/Authorization failed when checking permission with Identity Server. Status: {Status}",
                //     response.StatusCode);
                return false;
            }
            else
            {
                // _logger.LogWarning("Failed to check permission with Identity Server. Status: {Status}",
                //     response.StatusCode);

                // Try to read the error response
                try
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    // _logger.LogWarning("Error response from Identity Server: {ErrorContent}", errorContent);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Could not read error response: {ErrorMessage}", ex.Message);
                }

                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission with Identity Server: {Permission}", permissionName);
            return false;
        }
    }

    private class PermissionCheckResult
    {
        public bool IsGranted { get; set; }
    }

    private class MultiplePermissionCheckResult
    {
        public Dictionary<string, bool> Permissions { get; set; } = [];
    }
}