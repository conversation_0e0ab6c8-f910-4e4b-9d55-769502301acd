import { clsx } from "clsx"
import type { ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function cx(...args: ClassValue[]) {
  return twMerge(clsx(...args))
}

export const focusInput = [
  // base
  'focus:ring-2',
  // ring color
  'focus:ring-blue-200 dark:focus:ring-blue-700/30',
  // border color
  'focus:border-blue-500 dark:focus:border-blue-700',
]

// Tremor focusRing [v0.0.1]

export const focusRing = [
  // base
  'outline outline-offset-2 outline-0 focus-visible:outline-2',
  // outline color
  'outline-blue-500 dark:outline-blue-500',
]

// Tremor hasErrorInput [v0.0.1]

export const hasErrorInput = [
  // base
  'ring-2',
  // border color
  'border-red-500 dark:border-red-700',
  // ring color
  'ring-red-200 dark:ring-red-700/30',
]

interface CurrencyParams {
  number: number
  maxFractionDigits?: number
  currency?: string
}

interface PercentageParams {
  number: number
  decimals?: number
}

interface MillionParams {
  number: number
  decimals?: number
}

type FormatterFunctions = {
  currency: (params: CurrencyParams) => string
  unit: (number: number) => string
  percentage: (params: PercentageParams) => string
  million: (params: MillionParams) => string
}

export const formatters: FormatterFunctions = {
  currency: ({ number, maxFractionDigits = 2, currency = 'USD' }: CurrencyParams): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      maximumFractionDigits: maxFractionDigits,
    }).format(number)
  },

  unit: (number: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'decimal',
    }).format(number)
  },

  percentage: ({ number, decimals = 1 }: PercentageParams): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(number)
  },

  million: ({ number, decimals = 1 }: MillionParams): string => {
    return `${new Intl.NumberFormat('en-US', {
      style: 'decimal',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(number)}M`
  },
}

export const Permissions = {
  ROLES: 'AbpIdentity.Roles',
  USERS: 'AbpIdentity.Users',
  TENANTS: 'AbpTenantManagement.Tenants',
  MANAGE_HOST_FEATURES: 'FeatureManagement.ManageHostFeatures',
  SETTINGS: 'SettingManagement.Emailing',
  IDENTITY_PROVIDER_CLAIMS: 'IdentityServer.Claims',
  IDENTITY_PROVIDER_CLAIM_TYPES: 'IdentityServer.ClaimTypes',
  IDENTITY_PROVIDER_OPENIDDICT_APPLICATIONS: 'IdentityServer.OpenIddictApplications',
  IDENTITY_PROVIDER_OPENIDDICT_SCOPES: 'IdentityServer.OpenIddictScopes',
  IDENTITY_PROVIDER_OPENIDDICT_RESOURCES: 'IdentityServer.OpenIddictResources',
} as const

export const PermissionsGrant = {
  ROLE_MANAGEMENT: 'Role management',
  TENANT_MANAGEMENT: 'Tenant management',
  FEATURE_MANAGEMENT: 'Feature management',
  SETTING_MANAGEMENT: 'Setting management',
} as const

export const USER_ROLE = {
  ADMIN: 'admin',
} as const

export const PermissionProvider = {
  U: 'U',
  R: 'R',
  T: 'T',
} as const

/**
 * Helper method for creating a range of numbers
 * range(1, 5) => [1, 2, 3, 4, 5]
 */
const range = (from: number, to: number) => {
  let i = from
  const range = []

  while (i <= to) {
    range.push(i)
    i++
  }

  return range
}

export const getPages = (totalPages: number, currentPage: number): (number | 'SPACER')[] => {
  /**
   * totalNumbers: the total page numbers to show on the control
   * totalBlocks: totalNumbers + 2 to cover for the spacers
   */
  const totalNumbers = 5
  const totalBlocks = totalNumbers + 2

  if (totalPages > totalBlocks) {
    const startPage = Math.max(2, currentPage - 1)
    const endPage = Math.min(totalPages - 1, currentPage + 1)

    let pages: Array<number | 'SPACER'> = range(startPage, endPage)

    /**
     * hasLeftSpill: has hidden pages to the left
     * hasRightSpill: has hidden pages to the right
     * spillOffset: number of hidden pages either to the left or to the right
     */
    const hasLeftSpill = startPage > 2
    const hasRightSpill = totalPages - endPage > 1
    const spillOffset = totalNumbers - (pages.length + 3)
    switch (true) {
      // handle: (1) ... {6} [7] (8)
      case hasLeftSpill && !hasRightSpill: {
        const extraPages = range(startPage - spillOffset, startPage - 1)
        pages = ['SPACER', ...extraPages, ...pages]
        break
      }

      // handle: (1) {2} [3] {4} ... (8)
      case !hasLeftSpill && hasRightSpill: {
        const extraPages = range(endPage + 1, endPage + spillOffset)
        pages = [...pages, ...extraPages, 'SPACER']
        break
      }

      // handle: (1) ... {3} [4] {5} ... (8)
      case hasLeftSpill && hasRightSpill:
      default: {
        pages = ['SPACER', ...pages, 'SPACER']
        break
      }
    }

    return [1, ...pages, totalPages]
  }

  return range(1, totalPages)
}
