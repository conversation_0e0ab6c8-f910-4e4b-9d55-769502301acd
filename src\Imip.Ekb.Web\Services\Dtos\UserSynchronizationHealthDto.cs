using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Imip.Ekb.Web.Services.Dtos;

/// <summary>
/// DTO for user synchronization health check
/// </summary>
public class UserSynchronizationHealthDto
{
    /// <summary>
    /// Whether user synchronization is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Whether the service is healthy
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// Current configuration settings
    /// </summary>
    public UserSynchronizationConfigDto Configuration { get; set; } = new();

    /// <summary>
    /// Last synchronization timestamp
    /// </summary>
    public DateTime? LastSynchronization { get; set; }

    /// <summary>
    /// Total number of users synchronized
    /// </summary>
    public int TotalUsersSynchronized { get; set; }

    /// <summary>
    /// Health check timestamp
    /// </summary>
    public DateTime CheckTimestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Any error messages
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Configuration information for health check
/// </summary>
public class UserSynchronizationConfigDto
{
    /// <summary>
    /// Whether user synchronization is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Whether to update existing users
    /// </summary>
    public bool UpdateExistingUsers { get; set; }

    /// <summary>
    /// Whether to synchronize roles
    /// </summary>
    public bool SynchronizeRoles { get; set; }

    /// <summary>
    /// Whether to synchronize claims
    /// </summary>
    public bool SynchronizeClaims { get; set; }

    /// <summary>
    /// Whether logging is enabled
    /// </summary>
    public bool EnableLogging { get; set; }
}