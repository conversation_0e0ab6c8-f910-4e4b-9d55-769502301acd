using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Jetty.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Jetty;

public interface IJettyAppService :
    ICrudAppService<JettyDto, Guid, PagedAndSortedResultRequestDto, JettyCreateUpdateDto, JettyCreateUpdateDto>
{
    Task<PagedResultDto<JettyDto>> FilterListAsync(QueryParametersDto parameters);
}