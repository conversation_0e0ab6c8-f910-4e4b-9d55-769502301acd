using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Imip.Ekb.Web.Services.Attachments;

/// <summary>
/// DTO for file upload using multipart/form-data
/// </summary>
public class FileUploadFormDto
{
    /// <summary>
    /// The file to upload
    /// </summary>
    [Required]
    public IFormFile File { get; set; } = null!;

    /// <summary>
    /// Optional description of the file
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Optional reference ID (e.g., reservation ID, guest ID) that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type (e.g., "Reservation", "Guest") that this file is associated with
    /// </summary>
    [StringLength(50)]
    public string? ReferenceType { get; set; }
}
