using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.BcType.Dtos;

public class BcTypeDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Type { get; set; } = null!;
    public string CreatedBy { get; set; } = null!;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int? TransNo { get; set; }
    public string? TransName { get; set; }
    public string Status { get; set; } = null!;
}