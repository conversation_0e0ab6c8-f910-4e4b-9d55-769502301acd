using Imip.Ekb.BoundedZone.Dtos;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone;

public interface IVesselAppService : IApplicationService
{
    Task<PagedResultDto<VesselHeaderDto>> VesselHeadersAsync(VesselListRequestDto input);
    Task<PagedResultDto<VesselItemDto>> VesselItemsAsync(VesselListRequestDto input);
    Task<VesselHeaderDto> VesselHeaderAsync(Guid id, string vesselType);
    Task<VesselItemDto> VesselItemAsync(Guid id, string vesselType);
}