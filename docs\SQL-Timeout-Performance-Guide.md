# SQL Server Timeout Performance Guide

## Problem Analysis

The SQL Server timeout error you encountered is a common performance issue in .NET applications. Based on the error analysis, the timeout occurred in the `GetListAsync` method at line 102, specifically during the `AsyncExecuter.CountAsync(queryable)` operation.

### Root Causes

1. **Missing Database Indexes**: The `ZoneDetail` table lacks proper indexes for common query patterns
2. **Large Dataset Operations**: Counting all records before applying paging
3. **Inefficient Query Structure**: No optimization for read-only operations
4. **Soft Delete Filtering**: Filtering on `Deleted != "Y"` without proper indexing

## Solutions Implemented

### 1. Database Indexes

We've added the following indexes to improve query performance:

```sql
-- Composite index for common query patterns
CREATE INDEX IX_T_MDOC_Deleted_DocEntry ON T_MDOC (Deleted, DocEntry);

-- Index for soft delete filtering
CREATE INDEX IX_T_MDOC_Deleted ON T_MDOC (Deleted);

-- Index for sorting by DocEntry
CREATE INDEX IX_T_MDOC_DocEntry ON T_MDOC (DocEntry);

-- Index for creation time (used in default sorting)
CREATE INDEX IX_T_MDOC_CreationTime ON T_MDOC (CreationTime);

-- Indexes for common filter fields
CREATE INDEX IX_T_MDOC_Bp ON T_MDOC (BP);
CREATE INDEX IX_T_MDOC_BlNo ON T_MDOC (BL_No);
CREATE INDEX IX_T_MDOC_SppbNo ON T_MDOC (SPPB_No);
CREATE INDEX IX_T_MDOC_Status ON T_MDOC (Status);
```

### 2. Repository Optimizations

Added optimized methods to `IZoneDetailRepository`:

```csharp
public interface IZoneDetailRepository : IRepository<ZoneDetail, Guid>
{
    Task<IQueryable<ZoneDetail>> GetQueryableWithIncludesAsync();
    Task<IQueryable<ZoneDetail>> GetOptimizedQueryableAsync();
    Task<int> GetOptimizedCountAsync();
    Task<int> GetCountWithTimeoutAsync(int timeoutSeconds = 30);
}
```

### 3. Application Service Improvements

Enhanced `BoundedZoneAppService.GetListAsync()` with:

- Timeout protection using `CancellationTokenSource`
- Fallback strategies for large datasets
- Better error handling and logging
- Optimized query patterns

## Additional Recommendations

### 1. Database Configuration

```sql
-- Increase command timeout for long-running queries
-- Add to your connection string or configure in DbContext
SET COMMAND_TIMEOUT = 120; -- 2 minutes

-- Optimize SQL Server settings
-- Enable query store for performance monitoring
ALTER DATABASE [YourDatabase] SET QUERY_STORE = ON;
```

### 2. Connection String Optimization

```json
{
  "ConnectionStrings": {
    "Default": "Server=...;Database=...;Command Timeout=120;Max Pool Size=200;Min Pool Size=10;"
  }
}
```

### 3. Entity Framework Configuration

```csharp
// In your DbContext configuration
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    optionsBuilder
        .UseSqlServer(connectionString, options =>
        {
            options.CommandTimeout(120); // 2 minutes
            options.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        });
}
```

### 4. Caching Strategy

For frequently accessed data, implement caching:

```csharp
public class CachedZoneDetailService
{
    private readonly IMemoryCache _cache;
    private readonly IZoneDetailRepository _repository;
    
    public async Task<PagedResultDto<ZoneDetailDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var cacheKey = $"ZoneDetails_{input.SkipCount}_{input.MaxResultCount}_{input.Sorting}";
        
        if (_cache.TryGetValue(cacheKey, out PagedResultDto<ZoneDetailDto> cachedResult))
        {
            return cachedResult;
        }
        
        var result = await _repository.GetListAsync(input);
        _cache.Set(cacheKey, result, TimeSpan.FromMinutes(5));
        
        return result;
    }
}
```

### 5. Monitoring and Alerting

```csharp
// Add performance monitoring
public class PerformanceInterceptor : IInterceptor
{
    private readonly ILogger<PerformanceInterceptor> _logger;
    
    public void Intercept(IInvocation invocation)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            invocation.Proceed();
        }
        finally
        {
            stopwatch.Stop();
            
            if (stopwatch.ElapsedMilliseconds > 5000) // 5 seconds
            {
                _logger.LogWarning(
                    "Slow operation detected: {Method} took {Duration}ms",
                    invocation.Method.Name,
                    stopwatch.ElapsedMilliseconds);
            }
        }
    }
}
```

## Performance Testing

### 1. Query Performance Analysis

```sql
-- Check query execution plans
SET STATISTICS IO ON;
SET STATISTICS TIME ON;

-- Your query here
SELECT COUNT(*) FROM T_MDOC WHERE Deleted != 'Y';

SET STATISTICS IO OFF;
SET STATISTICS TIME OFF;
```

### 2. Index Usage Analysis

```sql
-- Check index usage
SELECT 
    OBJECT_NAME(i.object_id) AS TableName,
    i.name AS IndexName,
    ius.user_seeks,
    ius.user_scans,
    ius.user_lookups,
    ius.user_updates
FROM sys.dm_db_index_usage_stats ius
INNER JOIN sys.indexes i ON ius.object_id = i.object_id 
    AND ius.index_id = i.index_id
WHERE OBJECT_NAME(i.object_id) = 'T_MDOC';
```

## Troubleshooting Steps

1. **Check Database Size**: Large tables may need partitioning
2. **Monitor Query Plans**: Use SQL Server Profiler or Extended Events
3. **Review Index Fragmentation**: Rebuild indexes regularly
4. **Check for Blocking**: Monitor for long-running transactions
5. **Analyze Wait Statistics**: Identify bottlenecks

## Best Practices

1. **Always Use Paging**: Never return all records without limits
2. **Implement Proper Indexing**: Index columns used in WHERE, ORDER BY, and JOIN clauses
3. **Use AsNoTracking()**: For read-only operations
4. **Monitor Performance**: Set up alerts for slow queries
5. **Implement Caching**: For frequently accessed, rarely changed data
6. **Use Connection Pooling**: Configure appropriate pool sizes
7. **Set Reasonable Timeouts**: Balance between user experience and resource usage

## Migration Steps

1. Run the database migration to add indexes:
   ```bash
   dotnet ef database update
   ```

2. Monitor query performance after index creation

3. Consider implementing caching for frequently accessed data

4. Set up performance monitoring and alerting

5. Review and optimize other slow queries in the application 