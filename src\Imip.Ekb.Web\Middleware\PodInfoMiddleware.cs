using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Imip.Ekb.Web.Middleware;

public class PodInfoMiddleware
{
    private readonly RequestDelegate _next;
    private readonly string _podInfo;
    private readonly string _networkInfo;
    private readonly ILogger<PodInfoMiddleware> _logger;

    public PodInfoMiddleware(RequestDelegate next, ILogger<PodInfoMiddleware> logger)
    {
        _next = next;
        _logger = logger;

        try
        {
            // Get pod and node information
            var podName = Environment.GetEnvironmentVariable("HOSTNAME") ?? "unknown";
            var nodeName = Environment.GetEnvironmentVariable("NODE_NAME") ?? "unknown";
            _podInfo = $"Pod:{podName};Node:{nodeName}";

            // Get network information
            var hostName = Dns.GetHostName();
            var sb = new StringBuilder();
            sb.Append($"Host:{hostName};");

            try
            {
                // Get IP addresses
                var addresses = Dns.GetHostAddresses(hostName);
                sb.Append("IPs:");
                foreach (var ip in addresses)
                {
                    sb.Append($"{ip},");
                }

                // Test Redis connectivity to external Redis server
                try
                {
                    using (var ping = new Ping())
                    {
                        var reply = ping.Send("**********", 1000);
                        sb.Append($";RedisReachable:{reply.Status == IPStatus.Success}");
                    }
                }
                catch (Exception ex)
                {
                    sb.Append($";RedisError:{ex.Message}");
                    _logger.LogWarning(ex, "Error pinging Redis during middleware initialization");
                }
            }
            catch (Exception ex)
            {
                sb.Append($"NetworkError:{ex.Message}");
                _logger.LogWarning(ex, "Error getting network information during middleware initialization");
            }

            _networkInfo = sb.ToString();
            _logger.LogInformation("PodInfoMiddleware initialized with: {PodInfo}, {NetworkInfo}", _podInfo, _networkInfo);
        }
        catch (Exception ex)
        {
            _podInfo = "Error:" + ex.Message;
            _networkInfo = "Error:" + ex.Message;
            _logger.LogError(ex, "Error initializing PodInfoMiddleware");
        }
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Add pod info to response headers
            context.Response.OnStarting(() =>
            {
                context.Response.Headers.Append("X-Pod-Info", _podInfo);
                context.Response.Headers.Append("X-Network-Info", _networkInfo);
                return Task.CompletedTask;
            });

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in PodInfoMiddleware.InvokeAsync");
            throw;
        }
    }
}

public static class PodInfoMiddlewareExtensions
{
    public static IApplicationBuilder UsePodInfo(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<PodInfoMiddleware>();
    }
}
