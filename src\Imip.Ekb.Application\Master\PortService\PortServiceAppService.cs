using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.PortService.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.PortService;

[Authorize]
public class PortServiceAppService :
    CrudAppService<PortService, PortServiceDto, Guid, PagedAndSortedResultRequestDto, PortServiceCreateUpdateDto, PortServiceCreateUpdateDto>,
    IPortServiceAppService
{
    private readonly IPortServiceRepository _portServiceRepository;
    private readonly PortServiceMapper _mapper;
    private readonly ILogger<PortServiceAppService> _logger;

    public PortServiceAppService(
        IPortServiceRepository portServiceRepository,
        PortServiceMapper mapper,
        ILogger<PortServiceAppService> logger)
        : base(portServiceRepository)
    {
        _portServiceRepository = portServiceRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<PortServiceDto> CreateAsync(PortServiceCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _portServiceRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PortServiceDto> UpdateAsync(Guid id, PortServiceCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _portServiceRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _portServiceRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _portServiceRepository.GetAsync(id);
        entity.Active = "N";
        entity.UpdatedAt = Clock.Now;
        await _portServiceRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<PortServiceDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _portServiceRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<PortServiceDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _portServiceRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(PortService.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<PortServiceDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<PortServiceDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _portServiceRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<PortServiceDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<PortService> ApplyDynamicQuery(IQueryable<PortService> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<PortService>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<PortService>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}