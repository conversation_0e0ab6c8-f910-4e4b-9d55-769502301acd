﻿using Imip.Ekb.Master.BusinessPartner;
using Imip.Ekb.Master.BusinessPartner.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class BusinessPartnerMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    public partial BusinessPartnerDto MapToDto(BusinessPartner entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(BusinessPartner.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(BusinessPartner.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(BusinessPartner.CreatedAt))]
    public partial void MapToEntity(BusinessPartnerCreateUpdateDto dto, BusinessPartner entity);

    // Custom mapping methods for complex scenarios
    public BusinessPartner CreateEntityWithId(BusinessPartnerCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (BusinessPartner)Activator.CreateInstance(typeof(BusinessPartner), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<BusinessPartnerDto> MapToDtoList(List<BusinessPartner> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<BusinessPartnerDto> MapToDtoEnumerable(IEnumerable<BusinessPartner> entities);
}
