﻿using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Master.ItemClassification.Dtos;
public class ItemClassificationCreateUpdateDto
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(255)]
    public string? ReportType { get; set; }

    [StringLength(1)]
    public string Deleted { get; set; } = null!;

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public long? Category { get; set; }
}
