using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Imip.Ekb.Web.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Security.Claims;

namespace Imip.Ekb.Web.Services;

public class AuthenticationTokenValidationService : IAuthenticationTokenValidationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationTokenValidationService> _logger;
    private readonly IdentityUserManager _userManager;
    private readonly ICurrentTenant _currentTenant;
    private readonly IGuidGenerator _guidGenerator;

    public AuthenticationTokenValidationService(
        IConfiguration configuration,
        ILogger<AuthenticationTokenValidationService> logger,
        IdentityUserManager userManager,
        ICurrentTenant currentTenant,
        IGuidGenerator guidGenerator)
    {
        _configuration = configuration;
        _logger = logger;
        _userManager = userManager;
        _currentTenant = currentTenant;
        _guidGenerator = guidGenerator;
    }

    public async Task ValidateJwtBearerTokenAsync(Microsoft.AspNetCore.Authentication.JwtBearer.TokenValidatedContext context)
    {
        try
        {
            var claimsPrincipal = context.Principal;
            var email = GetEmailFromClaims(claimsPrincipal);
            var sub = GetSubjectFromClaims(claimsPrincipal);

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("JWT token validated but no email claim found");
                context.Fail("No email claim found in token");
                return;
            }

            // Find the user in our system
            var user = await _userManager.FindByEmailAsync(email);
            if (user != null && claimsPrincipal.Identity is ClaimsIdentity identity)
            {
                // Add ABP-specific claims to the existing principal
                AddAbpClaims(identity, user);

                _logger.LogInformation("JWT token validated successfully for user: {Email}", email);
            }
            else
            {
                _logger.LogWarning("JWT token validated but user not found in local system: {Email}", email);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing JWT token validation");
            context.Fail("Token validation failed");
        }
    }

    public async Task ValidateOpenIdConnectTokenAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context)
    {
        try
        {
            var serviceProvider = context.HttpContext.RequestServices;
            var claimsPrincipal = context.Principal;

            var email = GetEmailFromClaims(claimsPrincipal);
            var name = GetNameFromClaims(claimsPrincipal);
            var sub = GetSubjectFromClaims(claimsPrincipal);

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("OIDC token validated but no email claim found");
                context.Fail("No email claim found in token");
                return;
            }

            // Find or create user in our system
            var user = await FindOrCreateUserAsync(email, name, sub);
            if (user != null && claimsPrincipal.Identity is ClaimsIdentity identity)
            {
                // Add ABP-specific claims to the existing principal
                AddAbpClaims(identity, user);

                // Store tokens for later use
                await StoreTokensAsync(context, user, email);

                _logger.LogInformation("OIDC token validated successfully for user: {Email}", email);
            }
            else
            {
                _logger.LogError("Failed to find or create user for email: {Email}", email);
                context.Fail("User creation failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing OIDC token validation");
            context.Fail("Token validation failed");
        }
    }

    private string? GetEmailFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("email")?.Value ??
               principal?.FindFirst(ClaimTypes.Email)?.Value;
    }

    private string? GetNameFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("given_name")?.Value ??
           principal?.FindFirst("name")?.Value ??
           principal?.FindFirst(ClaimTypes.Name)?.Value ??
           principal?.FindFirst("family_name")?.Value;
    }

    private string? GetSubjectFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("sub")?.Value ??
               principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    private void AddAbpClaims(ClaimsIdentity identity, IdentityUser user)
    {
        // Remove existing ABP claims to avoid duplicates
        var existingAbpClaims = identity.Claims
            .Where(c => c.Type == AbpClaimTypes.UserId ||
                       c.Type == AbpClaimTypes.UserName ||
                       c.Type == AbpClaimTypes.Email)
            .ToList();

        foreach (var claim in existingAbpClaims)
        {
            identity.RemoveClaim(claim);
        }

        // Add ABP-specific claims
        identity.AddClaim(new Claim(AbpClaimTypes.UserId, user.Id.ToString()));
        identity.AddClaim(new Claim(AbpClaimTypes.UserName, user.UserName));
        identity.AddClaim(new Claim(AbpClaimTypes.Email, user.Email));

        if (!string.IsNullOrEmpty(user.Name))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.Name, user.Name));
        }

        if (!string.IsNullOrEmpty(user.Surname))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.SurName, user.Surname));
        }

        if (user.TenantId.HasValue)
        {
            identity.AddClaim(new Claim(AbpClaimTypes.TenantId, user.TenantId.Value.ToString()));
        }
    }

    private async Task<IdentityUser?> FindOrCreateUserAsync(string email, string? name, string? sub)
    {
        // First try to find by email
        var user = await _userManager.FindByIdAsync(sub);
        if (user != null)
        {
            // Update existing user information if needed
            var needsUpdate = false;

            if (!string.IsNullOrEmpty(name) && user.Name != name)
            {
                user.Name = name;
                user.SetProperty("DisplayName", name);
                needsUpdate = true;
            }

            // Update external user ID if it changed
            var currentExternalId = user.GetProperty<string>("ExternalUserId");
            if (!string.IsNullOrEmpty(sub) && currentExternalId != sub)
            {
                user.SetProperty("ExternalUserId", sub);
                needsUpdate = true;
            }

            if (needsUpdate)
            {
                await _userManager.UpdateAsync(user);
            }

            return user;
        }

        // If not found, create new user
        try
        {
            _logger.LogInformation("Creating new user for email: {Email}, external ID: {ExternalId}", email, sub);

            // Parse userId string to Guid
            if (!string.IsNullOrEmpty(sub) && Guid.TryParse(sub, out var userGuid))
            {
                // Use the external user ID as the internal ID
                user = new IdentityUser(
                    userGuid,
                    email,
                    email,
                    tenantId: _currentTenant.Id
                );
            }
            else
            {
                // Generate a new ID if external ID is not a valid GUID
                user = new IdentityUser(
                    _guidGenerator.Create(),
                    email,
                    email,
                    tenantId: _currentTenant.Id
                );
            }

            if (!string.IsNullOrEmpty(name))
            {
                user.Name = name;
                user.SetProperty("DisplayName", name);
            }

            // Store the external user ID for future reference
            if (!string.IsNullOrEmpty(sub))
            {
                user.SetProperty("ExternalUserId", sub);
            }

            user.SetEmailConfirmed(true); // Since it's from SSO, consider email verified

            var result = await _userManager.CreateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("Created new user from SSO: {Email}", email);
                return user;
            }
            else
            {
                _logger.LogError("Failed to create user {Email}: {Errors}",
                    email, string.Join(", ", result.Errors.Select(e => e.Description)));
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception creating user {Email}", email);
            return null;
        }
    }

    private async Task StoreTokensAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context, IdentityUser user, string email)
    {
        try
        {
            // Store tokens for later use
            if (context.Properties?.Items.ContainsKey(".Token.access_token") == true)
            {
                var accessToken = context.Properties.Items[".Token.access_token"];
                var refreshToken = context.Properties.Items.ContainsKey(".Token.refresh_token")
                    ? context.Properties.Items[".Token.refresh_token"]
                    : null;

                _logger.LogInformation("Access token stored for user: {Email}", email);

                // Store tokens in user properties for later use
                user.SetProperty("AccessToken", accessToken);
                if (!string.IsNullOrEmpty(refreshToken))
                {
                    user.SetProperty("RefreshToken", refreshToken);
                }

                // Update user with token information
                await _userManager.UpdateAsync(user);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing tokens for user: {Email}", email);
            // Don't fail the authentication if token storage fails
        }
    }
}
