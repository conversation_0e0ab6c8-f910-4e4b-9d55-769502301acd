namespace Imip.Ekb.Users;

/// <summary>
/// Configuration options for user synchronization
/// </summary>
public class UserSynchronizationOptions
{
    /// <summary>
    /// Whether user synchronization is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Whether to update existing users during synchronization
    /// </summary>
    public bool UpdateExistingUsers { get; set; } = true;

    /// <summary>
    /// Whether to synchronize user roles
    /// </summary>
    public bool SynchronizeRoles { get; set; } = true;

    /// <summary>
    /// Whether to synchronize user claims
    /// </summary>
    public bool SynchronizeClaims { get; set; } = true;

    /// <summary>
    /// Whether to log user synchronization activities
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// Paths to skip during user synchronization
    /// </summary>
    public string[] SkipPaths { get; set; } = new[]
    {
        "/favicon.ico",
        "/robots.txt",
        "/_framework",
        "/css/",
        "/js/",
        "/images/",
        "/fonts/",
        "/lib/",
        "/swagger",
        "/health",
        "/metrics",
        "/connect/token",
        "/connect/userinfo",
        "/connect/authorize",
        "/.well-known"
    };

    /// <summary>
    /// Maximum number of retry attempts for user synchronization
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts in milliseconds
    /// </summary>
    public int RetryDelayMilliseconds { get; set; } = 1000;
}

