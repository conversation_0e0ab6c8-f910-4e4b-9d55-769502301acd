﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Imip.Ekb.TempModels;

public partial class TempDbContext : DbContext
{
    public TempDbContext()
    {
    }

    public TempDbContext(DbContextOptions<TempDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AclRule> AclRules { get; set; }

    public virtual DbSet<ActivityLog> ActivityLogs { get; set; }

    public virtual DbSet<ApiLog> ApiLogs { get; set; }

    public virtual DbSet<AppChangeLog> AppChangeLogs { get; set; }

    public virtual DbSet<AssisTug> AssisTugs { get; set; }

    public virtual DbSet<Audit> Audits { get; set; }

    public virtual DbSet<BatchApproval> BatchApprovals { get; set; }

    public virtual DbSet<Bexp> Bexps { get; set; }

    public virtual DbSet<Bexp1> Bexp1s { get; set; }

    public virtual DbSet<Bhexp> Bhexps { get; set; }

    public virtual DbSet<Bhimp> Bhimps { get; set; }

    public virtual DbSet<Bhlocal> Bhlocals { get; set; }

    public virtual DbSet<Bimp> Bimps { get; set; }

    public virtual DbSet<Bimp1> Bimp1s { get; set; }

    public virtual DbSet<BlTenantChange> BlTenantChanges { get; set; }

    public virtual DbSet<Cache> Caches { get; set; }

    public virtual DbSet<CatalogImg> CatalogImgs { get; set; }

    public virtual DbSet<Department> Departments { get; set; }

    public virtual DbSet<DocCheckError> DocCheckErrors { get; set; }

    public virtual DbSet<DocCheckErrorHeader> DocCheckErrorHeaders { get; set; }

    public virtual DbSet<DocQtyInv> DocQtyInvs { get; set; }

    public virtual DbSet<DocSignCoordinate> DocSignCoordinates { get; set; }

    public virtual DbSet<DocumentApproval> DocumentApprovals { get; set; }

    public virtual DbSet<DocumentApprovalItem> DocumentApprovalItems { get; set; }

    public virtual DbSet<Employee> Employees { get; set; }

    public virtual DbSet<EmployeeWorkShift> EmployeeWorkShifts { get; set; }

    public virtual DbSet<ExportClassification> ExportClassifications { get; set; }

    public virtual DbSet<FailedJob> FailedJobs { get; set; }

    public virtual DbSet<FormCancel> FormCancels { get; set; }

    public virtual DbSet<FormCancelItem> FormCancelItems { get; set; }

    public virtual DbSet<FormStatement> FormStatements { get; set; }

    public virtual DbSet<FormStatementHeader> FormStatementHeaders { get; set; }

    public virtual DbSet<HsCodeType> HsCodeTypes { get; set; }

    public virtual DbSet<ItemMasterSap> ItemMasterSaps { get; set; }

    public virtual DbSet<Job> Jobs { get; set; }

    public virtual DbSet<JobBatch> JobBatches { get; set; }

    public virtual DbSet<LEsign> LEsigns { get; set; }

    public virtual DbSet<LMaster> LMasters { get; set; }

    public virtual DbSet<LMdoc> LMdocs { get; set; }

    public virtual DbSet<LMdocHeader> LMdocHeaders { get; set; }

    public virtual DbSet<LProcessDoc> LProcessDocs { get; set; }

    public virtual DbSet<LProcessEsign> LProcessEsigns { get; set; }

    public virtual DbSet<ListPermission> ListPermissions { get; set; }

    public virtual DbSet<Log> Logs { get; set; }

    public virtual DbSet<MAgent> MAgents { get; set; }

    public virtual DbSet<MBp> MBps { get; set; }

    public virtual DbSet<MCargo> MCargos { get; set; }

    public virtual DbSet<MCatClassification> MCatClassifications { get; set; }

    public virtual DbSet<MCatalog> MCatalogs { get; set; }

    public virtual DbSet<MCatalogDetail> MCatalogDetails { get; set; }

    public virtual DbSet<MCategoryItem> MCategoryItems { get; set; }

    public virtual DbSet<MClassification> MClassifications { get; set; }

    public virtual DbSet<MCountry> MCountries { get; set; }

    public virtual DbSet<MCurrency> MCurrencies { get; set; }

    public virtual DbSet<MDestinationPort> MDestinationPorts { get; set; }

    public virtual DbSet<MDisplayForm> MDisplayForms { get; set; }

    public virtual DbSet<MDocAttachment> MDocAttachments { get; set; }

    public virtual DbSet<MExchangeRate> MExchangeRates { get; set; }

    public virtual DbSet<MJetty> MJetties { get; set; }

    public virtual DbSet<MLetter> MLetters { get; set; }

    public virtual DbSet<MLocalItem> MLocalItems { get; set; }

    public virtual DbSet<MMt> MMts { get; set; }

    public virtual DbSet<MPortOfLoading> MPortOfLoadings { get; set; }

    public virtual DbSet<MPpjk> MPpjks { get; set; }

    public virtual DbSet<MRemark> MRemarks { get; set; }

    public virtual DbSet<MRepresentative> MRepresentatives { get; set; }

    public virtual DbSet<MSigner> MSigners { get; set; }

    public virtual DbSet<MTbc> MTbcs { get; set; }

    public virtual DbSet<MTenant> MTenants { get; set; }

    public virtual DbSet<MTenantDocumentConfig> MTenantDocumentConfigs { get; set; }

    public virtual DbSet<MTenantLetter> MTenantLetters { get; set; }

    public virtual DbSet<MTongkang> MTongkangs { get; set; }

    public virtual DbSet<MTransType> MTransTypes { get; set; }

    public virtual DbSet<MTugboat> MTugboats { get; set; }

    public virtual DbSet<MUserDisplayForm> MUserDisplayForms { get; set; }

    public virtual DbSet<MUserPpjk> MUserPpjks { get; set; }

    public virtual DbSet<MUserTenant> MUserTenants { get; set; }

    public virtual DbSet<MasterGroup> MasterGroups { get; set; }

    public virtual DbSet<MasterHsCode> MasterHsCodes { get; set; }

    public virtual DbSet<MasterHsCodeName> MasterHsCodeNames { get; set; }

    public virtual DbSet<MasterRateUsdConvert> MasterRateUsdConverts { get; set; }

    public virtual DbSet<MasterSurveyor> MasterSurveyors { get; set; }

    public virtual DbSet<MasterTempImport> MasterTempImports { get; set; }

    public virtual DbSet<MasterTenantDocValid> MasterTenantDocValids { get; set; }

    public virtual DbSet<MasterTrading> MasterTradings { get; set; }

    public virtual DbSet<MasterWarehouse> MasterWarehouses { get; set; }

    public virtual DbSet<Migration> Migrations { get; set; }

    public virtual DbSet<ModelHasPermission> ModelHasPermissions { get; set; }

    public virtual DbSet<ModelHasRole> ModelHasRoles { get; set; }

    public virtual DbSet<MonitorDocWh> MonitorDocWhs { get; set; }

    public virtual DbSet<Mp> Mps { get; set; }

    public virtual DbSet<Mplat> Mplats { get; set; }

    public virtual DbSet<Mplp> Mplps { get; set; }

    public virtual DbSet<Mplsl> Mplsls { get; set; }

    public virtual DbSet<Mpltenant> Mpltenants { get; set; }

    public virtual DbSet<Notification> Notifications { get; set; }

    public virtual DbSet<NotificationDb> NotificationDbs { get; set; }

    public virtual DbSet<Notul> Notuls { get; set; }

    public virtual DbSet<NotulHeader> NotulHeaders { get; set; }

    public virtual DbSet<OPermission> OPermissions { get; set; }

    public virtual DbSet<ORole> ORoles { get; set; }

    public virtual DbSet<OauthAccessToken> OauthAccessTokens { get; set; }

    public virtual DbSet<OauthAuthCode> OauthAuthCodes { get; set; }

    public virtual DbSet<OauthClient> OauthClients { get; set; }

    public virtual DbSet<OauthPersonalAccessClient> OauthPersonalAccessClients { get; set; }

    public virtual DbSet<OauthRefreshToken> OauthRefreshTokens { get; set; }

    public virtual DbSet<PasswordChange> PasswordChanges { get; set; }

    public virtual DbSet<PasswordReset> PasswordResets { get; set; }

    public virtual DbSet<Permission> Permissions { get; set; }

    public virtual DbSet<PersonalAccessToken> PersonalAccessTokens { get; set; }

    public virtual DbSet<PostingPeriod> PostingPeriods { get; set; }

    public virtual DbSet<QueueMonitor> QueueMonitors { get; set; }

    public virtual DbSet<RInv> RInvs { get; set; }

    public virtual DbSet<RMaster> RMasters { get; set; }

    public virtual DbSet<ReleaseNote> ReleaseNotes { get; set; }

    public virtual DbSet<ReleaseNoteReceiver> ReleaseNoteReceivers { get; set; }

    public virtual DbSet<RevisionTransaction> RevisionTransactions { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<SerialNumber> SerialNumbers { get; set; }

    public virtual DbSet<Session> Sessions { get; set; }

    public virtual DbSet<SessionLog> SessionLogs { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<SignLogging> SignLoggings { get; set; }

    public virtual DbSet<Signature> Signatures { get; set; }

    public virtual DbSet<TMdoc> TMdocs { get; set; }

    public virtual DbSet<TMdoc1> TMdoc1s { get; set; }

    public virtual DbSet<TMdocBl> TMdocBls { get; set; }

    public virtual DbSet<TMdocBz> TMdocBzs { get; set; }

    public virtual DbSet<TMdocHeader> TMdocHeaders { get; set; }

    public virtual DbSet<TMdocHeaderBl> TMdocHeaderBls { get; set; }

    public virtual DbSet<TMdocHeaderInv> TMdocHeaderInvs { get; set; }

    public virtual DbSet<TMdocHeaderInvSub> TMdocHeaderInvSubs { get; set; }

    public virtual DbSet<TMdocHeaderLocal> TMdocHeaderLocals { get; set; }

    public virtual DbSet<TMdocInv> TMdocInvs { get; set; }

    public virtual DbSet<TMdocInvSub> TMdocInvSubs { get; set; }

    public virtual DbSet<TMdocLog> TMdocLogs { get; set; }

    public virtual DbSet<TMdocProcess> TMdocProcesses { get; set; }

    public virtual DbSet<TMdocRef> TMdocRefs { get; set; }

    public virtual DbSet<TMdocSign> TMdocSigns { get; set; }

    public virtual DbSet<TSapKb> TSapKbs { get; set; }

    public virtual DbSet<Texp> Texps { get; set; }

    public virtual DbSet<Thexp> Thexps { get; set; }

    public virtual DbSet<TimeSheet> TimeSheets { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserDepartment> UserDepartments { get; set; }

    public virtual DbSet<UserEmail> UserEmails { get; set; }

    public virtual DbSet<VwBasetableTransaction> VwBasetableTransactions { get; set; }

    public virtual DbSet<VwBasetableTransactionV2> VwBasetableTransactionV2s { get; set; }

    public virtual DbSet<VwBasetableTransactionV3> VwBasetableTransactionV3s { get; set; }

    public virtual DbSet<VwDataImport> VwDataImports { get; set; }

    public virtual DbSet<VwDataTemporaryExport> VwDataTemporaryExports { get; set; }

    public virtual DbSet<VwDocTransactionHeader> VwDocTransactionHeaders { get; set; }

    public virtual DbSet<VwDocTransactionValue> VwDocTransactionValues { get; set; }

    public virtual DbSet<VwDocTransactionValueV2> VwDocTransactionValueV2s { get; set; }

    public virtual DbSet<VwDocTransactionValueV3> VwDocTransactionValueV3s { get; set; }

    public virtual DbSet<VwLampiranTimbun> VwLampiranTimbuns { get; set; }

    public virtual DbSet<VwRecapReexportReimport> VwRecapReexportReimports { get; set; }

    public virtual DbSet<WebsocketsStatisticsEntry> WebsocketsStatisticsEntries { get; set; }

    public virtual DbSet<WorkShift> WorkShifts { get; set; }

    public virtual DbSet<WorkShiftDetail> WorkShiftDetails { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Server=localhost;Database=EKB_PRD;User ID=sa;Password=******;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AclRule>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__acl_rule__3213E83F0C9FF80C");

            entity.HasOne(d => d.User).WithMany(p => p.AclRules).HasConstraintName("acl_rules_user_id_foreign");
        });

        modelBuilder.Entity<ActivityLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__activity__3213E83F2AB308CB");
        });

        modelBuilder.Entity<ApiLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__api_logs__3213E83F6AC9A0AB");
        });

        modelBuilder.Entity<AppChangeLog>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__AppChang__F4D96FAE364C9CDB");

            entity.Property(e => e.AppName).HasDefaultValue("EKB");
        });

        modelBuilder.Entity<AssisTug>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__AssisTug__F4D96FAE9324FE9F");
        });

        modelBuilder.Entity<Audit>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__audits__3213E83FCB492A83");
        });

        modelBuilder.Entity<BatchApproval>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__batch_ap__3213E83FE27B1C95");

            entity.Property(e => e.DocumentType).HasDefaultValue("import");
        });

        modelBuilder.Entity<Bexp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BEXP__F4D96FAEB0AA268F");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.StatusServiceLoading).HasDefaultValue("Open");
            entity.Property(e => e.Total).HasDefaultValueSql("('0')");
            entity.Property(e => e.TotalServiceLoading).HasDefaultValueSql("('0')");
            entity.Property(e => e.Type).HasDefaultValue("Export");
            entity.Property(e => e.WeightCategory).HasDefaultValue("B/L Weight");
        });

        modelBuilder.Entity<Bexp1>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BEXP1__F4D96FAE44B0F95C");

            entity.Property(e => e.TotalServiceLoading).HasDefaultValueSql("('0')");
            entity.Property(e => e.Type).HasDefaultValue("Export");
            entity.Property(e => e.WeightCategory).HasDefaultValue("B/L Weight");
        });

        modelBuilder.Entity<Bhexp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BHEXP__F4D96FAE4B1E14CB");

            entity.Property(e => e.Type).HasDefaultValue("Export");
        });

        modelBuilder.Entity<Bhimp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BHIMP__F4D96FAE64E4A757");
        });

        modelBuilder.Entity<Bhlocal>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BHLOCAL__F4D96FAEEB67B181");
        });

        modelBuilder.Entity<Bimp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BIMP__F4D96FAEE90BD9AF");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.Total).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<Bimp1>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__BIMP1__F4D96FAE3A0ECB18");
        });

        modelBuilder.Entity<CatalogImg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__catalog___3213E83F17DD0448");
        });

        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Departme__3213E83F5F590D72");
        });

        modelBuilder.Entity<DocCheckError>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__doc_chec__3213E83F13368AD3");
        });

        modelBuilder.Entity<DocCheckErrorHeader>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__doc_chec__3213E83FF848CBB7");
        });

        modelBuilder.Entity<DocQtyInv>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__DocQtyIn__F4D96FAE7B224C2E");
        });

        modelBuilder.Entity<DocSignCoordinate>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__DocSignC__F4D96FAE96ED4F06");
        });

        modelBuilder.Entity<DocumentApproval>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Document__3213E83F331C4292");

            entity.Property(e => e.DocumentStatus).HasDefaultValue("Waiting");
        });

        modelBuilder.Entity<DocumentApprovalItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Document__3213E83F2CF2458C");

            entity.Property(e => e.Status).HasDefaultValue("Waiting");

            entity.HasOne(d => d.DocumentApproval).WithMany(p => p.DocumentApprovalItems)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("documentapprovalitem_document_approval_id_foreign");
        });

        modelBuilder.Entity<Employee>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Employee__3213E83F1D72F428");

            entity.Property(e => e.OrderLine).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<EmployeeWorkShift>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Employee__3213E83FF172ECB4");
        });

        modelBuilder.Entity<ExportClassification>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("exportclassification_id_primary");

            entity.Property(e => e.Id).IsFixedLength();
            entity.Property(e => e.Status).HasDefaultValue("Active");
        });

        modelBuilder.Entity<FailedJob>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__failed_j__3213E83F6A7E4769");

            entity.Property(e => e.FailedAt).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<FormCancel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__form_can__3213E83F328F39E2");

            entity.Property(e => e.Status).HasDefaultValue("Submitted");
        });

        modelBuilder.Entity<FormCancelItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__form_can__3213E83F7E0BB965");
        });

        modelBuilder.Entity<FormStatement>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__FormStat__F4D96FAE6BB19EEA");
        });

        modelBuilder.Entity<FormStatementHeader>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__FormStat__F4D96FAE5DB262B0");

            entity.Property(e => e.Status).HasDefaultValue("Submitted");
        });

        modelBuilder.Entity<HsCodeType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__hs_code___3213E83FADD78FEE");
        });

        modelBuilder.Entity<ItemMasterSap>(entity =>
        {
            entity.Property(e => e.Active)
                .HasDefaultValue("Y")
                .IsFixedLength();
            entity.Property(e => e.FrgnName).UseCollation("Chinese_Simplified_Stroke_Order_100_CS_AS_KS_WS_SC");
            entity.Property(e => e.ItemName).UseCollation("Chinese_Simplified_Pinyin_100_CS_AS_KS_WS_SC");
        });

        modelBuilder.Entity<Job>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__jobs__3213E83FB21BA46A");
        });

        modelBuilder.Entity<JobBatch>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("job_batches_id_primary");
        });

        modelBuilder.Entity<LEsign>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__L_esign__F4D96FAEEA7F4F0C");

            entity.Property(e => e.IdDownload).HasDefaultValue("N");
            entity.Property(e => e.IdRequest).HasDefaultValue("N");
            entity.Property(e => e.Revision).HasDefaultValue("origin");
        });

        modelBuilder.Entity<LMaster>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__L_master__F4D96FAE55BBFD59");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.InvoiceStatus).HasDefaultValue("open");
            entity.Property(e => e.StatusBms).HasDefaultValue("open");
        });

        modelBuilder.Entity<LProcessDoc>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__L_Proces__F4D96FAEBC0DD669");
        });

        modelBuilder.Entity<LProcessEsign>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__L_proces__F4D96FAE18C30D1C");
        });

        modelBuilder.Entity<ListPermission>(entity =>
        {
            entity.ToView("list_permissions");
        });

        modelBuilder.Entity<Log>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__logs__3213E83F0B69A1F9");
        });

        modelBuilder.Entity<MAgent>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Agent__F4D96FAEB6C7CB8A");

            entity.Property(e => e.Status).HasDefaultValue("Y");
            entity.Property(e => e.TaxCode).HasDefaultValue("SO11");
        });

        modelBuilder.Entity<MBp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_BP__F4D96FAE2BE575E1");

            entity.Property(e => e.RegionType).HasDefaultValue("Int");
            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MCargo>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_CARGO__F4D96FAECC5C7762");

            entity.Property(e => e.GrossWeight).HasDefaultValueSql("('0')");
            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MCatClassification>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_CatCla__F4D96FAEA8E7CD4A");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<MCatalog>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Catalo__F4D96FAE42055CB7");

            entity.Property(e => e.PermissionIdCreator).HasDefaultValueSql("('3')");
        });

        modelBuilder.Entity<MCatalogDetail>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__m_catalo__F4D96FAEDD4156EF");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<MCategoryItem>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Catego__F4D96FAEA642CE73");

            entity.Property(e => e.CategoryType).HasDefaultValue("Import");
        });

        modelBuilder.Entity<MClassification>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Classi__F4D96FAE5E689CD8");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<MCountry>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Countr__F4D96FAE7779E0CC");
        });

        modelBuilder.Entity<MCurrency>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Curren__F4D96FAE293A7A94");

            entity.Property(e => e.Active).HasDefaultValue("Y");
        });

        modelBuilder.Entity<MDestinationPort>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Destin__F4D96FAE8D79D759");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocType).HasDefaultValue("Export");
        });

        modelBuilder.Entity<MDisplayForm>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Displa__F4D96FAE41780B04");
        });

        modelBuilder.Entity<MDocAttachment>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Doc_at__F4D96FAE6A7FEEAD");

            entity.Property(e => e.DocType).HasDefaultValue("Import");
            entity.Property(e => e.TransType).HasDefaultValue("Inv");
        });

        modelBuilder.Entity<MExchangeRate>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Exchan__F4D96FAEAAEEF29F");

            entity.Property(e => e.EurPrice).HasDefaultValueSql("('0')");
            entity.Property(e => e.IdrPrice).HasDefaultValueSql("('0')");
            entity.Property(e => e.RmbPrice).HasDefaultValueSql("('0')");
            entity.Property(e => e.UsdPrice).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<MJetty>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Jetty__F4D96FAE83F626A8");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.Max).HasDefaultValueSql("('0')");
            entity.Property(e => e.Port).HasDefaultValue("Fatufia");
        });

        modelBuilder.Entity<MLetter>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Letter__F4D96FAEDA9E0E95");

            entity.Property(e => e.LetterType).HasDefaultValue("LPB");
        });

        modelBuilder.Entity<MLocalItem>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_LocalI__F4D96FAE35A28F78");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<MMt>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_MT__F4D96FAE01A37DD9");

            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MPortOfLoading>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_PortOf__F4D96FAE4C546EF1");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocType).HasDefaultValue("Local");
        });

        modelBuilder.Entity<MPpjk>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_PPJK__F4D96FAE6C4F1575");

            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MRemark>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Remark__F4D96FAEEA1BABD1");

            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MRepresentative>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Repres__F4D96FAE1FB0E40E");
        });

        modelBuilder.Entity<MSigner>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Signer__F4D96FAE126D86B6");
        });

        modelBuilder.Entity<MTbc>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_TBC__F4D96FAE0A860EDD");

            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MTenant>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Tenant__F4D96FAE9AD0BE2A");

            entity.Property(e => e.IsExternal).HasDefaultValue("N");
            entity.Property(e => e.IsTenant).HasDefaultValue("Y");
            entity.Property(e => e.Skbpph).HasDefaultValue("N");
            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
            entity.Property(e => e.UsePrivy).HasDefaultValue("N");
        });

        modelBuilder.Entity<MTenantDocumentConfig>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Tenant__F4D96FAE49F76F72");

            entity.Property(e => e.FooterHorizontalPos).HasDefaultValue("center");
            entity.Property(e => e.FooterMarginBottom).HasDefaultValueSql("('0')");
            entity.Property(e => e.FooterMarginLeft).HasDefaultValueSql("('0')");
            entity.Property(e => e.FooterPosition).HasDefaultValue("absolute");
            entity.Property(e => e.FooterVerticalPos).HasDefaultValue("bottom");
            entity.Property(e => e.HeaderHorizontalPos).HasDefaultValue("center");
            entity.Property(e => e.HeaderMarginLeft).HasDefaultValueSql("('0')");
            entity.Property(e => e.HeaderMarginTop).HasDefaultValueSql("('0')");
            entity.Property(e => e.HeaderPosition).HasDefaultValue("absolute");
            entity.Property(e => e.HeaderVerticalPos).HasDefaultValue("top");
            entity.Property(e => e.WatermarkHorizontalPos).HasDefaultValue("center");
            entity.Property(e => e.WatermarkMarginLeft).HasDefaultValueSql("('0')");
            entity.Property(e => e.WatermarkMarginTop).HasDefaultValueSql("('0')");
            entity.Property(e => e.WatermarkOpacity).HasDefaultValueSql("('50')");
            entity.Property(e => e.WatermarkPosition).HasDefaultValue("absolute");
            entity.Property(e => e.WatermarkVerticalPos).HasDefaultValue("center");
            entity.Property(e => e.WatermarkWrappingStyle).HasDefaultValue("behind");
        });

        modelBuilder.Entity<MTenantLetter>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Tenant__F4D96FAED4BE0AEC");

            entity.Property(e => e.Meterai).HasDefaultValue("N");
        });

        modelBuilder.Entity<MTongkang>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Tongka__F4D96FAE387E6919");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.GrossWeight).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<MTransType>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Trans___F4D96FAE3E5F5B6C");

            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<MTugboat>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_Tugboa__F4D96FAECC0606B0");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.GrossWeight).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<MUserDisplayForm>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_UserDi__F4D96FAE0AD69B8E");
        });

        modelBuilder.Entity<MUserPpjk>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_UserPp__F4D96FAE10E9605F");
        });

        modelBuilder.Entity<MUserTenant>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__M_User_t__F4D96FAE4AD5A0D0");

            entity.HasOne(d => d.TenantKeyNavigation).WithMany(p => p.MUserTenants)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("m_user_tenant_tenant_key_foreign");

            entity.HasOne(d => d.UserKeyNavigation).WithMany(p => p.MUserTenants)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("m_user_tenant_user_key_foreign");
        });

        modelBuilder.Entity<MasterGroup>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MasterGr__F4D96FAEC55277E9");

            entity.Property(e => e.Status).HasDefaultValue("Y");
        });

        modelBuilder.Entity<MasterHsCode>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__master_h__F4D96FAE4702EE5A");

            entity.Property(e => e.Skbpph).HasDefaultValue("N");
            entity.Property(e => e.Skbppn).HasDefaultValue("N");
        });

        modelBuilder.Entity<MasterHsCodeName>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MasterHs__F4D96FAEE0B6106B");
        });

        modelBuilder.Entity<MasterRateUsdConvert>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("masterrateusdconvert_id_primary");

            entity.Property(e => e.Id).IsFixedLength();
        });

        modelBuilder.Entity<MasterSurveyor>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__master_s__3213E83FF37274F4");

            entity.Property(e => e.IsActive).HasDefaultValue("Yes");
        });

        modelBuilder.Entity<MasterTempImport>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MasterTe__F4D96FAE7E1ADDF9");
        });

        modelBuilder.Entity<MasterTenantDocValid>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("mastertenantdocvalid_id_primary");

            entity.Property(e => e.Id).IsFixedLength();
            entity.Property(e => e.DocType).HasDefaultValue("SKBPPH");
            entity.Property(e => e.Status).HasDefaultValue("Y");
        });

        modelBuilder.Entity<MasterTrading>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__master_t__3213E83F5F7D92A8");

            entity.Property(e => e.IsActive).HasDefaultValue("Yes");
        });

        modelBuilder.Entity<MasterWarehouse>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MasterWa__F4D96FAEE94BF9F8");

            entity.Property(e => e.Status).HasDefaultValue("Y");
        });

        modelBuilder.Entity<Migration>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__migratio__3213E83F0B494F50");
        });

        modelBuilder.Entity<ModelHasPermission>(entity =>
        {
            entity.HasKey(e => new { e.PermissionId, e.ModelId, e.ModelType }).HasName("model_has_permissions_permission_model_type_primary");

            entity.HasOne(d => d.Permission).WithMany(p => p.ModelHasPermissions).HasConstraintName("model_has_permissions_permission_id_foreign");
        });

        modelBuilder.Entity<ModelHasRole>(entity =>
        {
            entity.HasKey(e => new { e.RoleId, e.ModelId, e.ModelType }).HasName("model_has_roles_role_model_type_primary");

            entity.HasOne(d => d.Role).WithMany(p => p.ModelHasRoles).HasConstraintName("model_has_roles_role_id_foreign");
        });

        modelBuilder.Entity<MonitorDocWh>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MonitorD__F4D96FAE17BC5DFD");
        });

        modelBuilder.Entity<Mp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MPS__F4D96FAEC904ADA1");

            entity.Property(e => e.Active).HasDefaultValue("Y");
        });

        modelBuilder.Entity<Mplat>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MPLAT__F4D96FAEB491CF38");
        });

        modelBuilder.Entity<Mplp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MPLPS__F4D96FAEA9347760");

            entity.Property(e => e.BillingType).HasDefaultValue("Import");
            entity.Property(e => e.Currency).HasDefaultValueSql("('1')");
            entity.Property(e => e.Type).HasDefaultValue("Global");
        });

        modelBuilder.Entity<Mplsl>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MPLSL__F4D96FAE9542EE45");

            entity.Property(e => e.BillingType).HasDefaultValue("Import");
            entity.Property(e => e.Currency).HasDefaultValueSql("('2')");
            entity.Property(e => e.Type).HasDefaultValue("Global");
        });

        modelBuilder.Entity<Mpltenant>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__MPLTENAN__F4D96FAE131D92FE");

            entity.Property(e => e.Currency).HasDefaultValueSql("('1')");
        });

        modelBuilder.Entity<Notification>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__Notifica__F4D96FAEA79F7F5F");

            entity.Property(e => e.DocType).HasDefaultValue("Document");
            entity.Property(e => e.Read).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<NotificationDb>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("notification_db_id_primary");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<Notul>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__notuls__3213E83F2292227B");
        });

        modelBuilder.Entity<NotulHeader>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__notul_he__3213E83F8496ECE6");
        });

        modelBuilder.Entity<OPermission>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__O_Permis__F4D96FAE474084CE");
        });

        modelBuilder.Entity<ORole>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__O_Roles__F4D96FAEE6767ED0");

            entity.Property(e => e.HasChildren)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.HasRoute)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.Model).HasDefaultValueSql("('0')");
            entity.Property(e => e.Status)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<OauthAccessToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("oauth_access_tokens_id_primary");
        });

        modelBuilder.Entity<OauthAuthCode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("oauth_auth_codes_id_primary");
        });

        modelBuilder.Entity<OauthClient>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("oauth_clients_id_primary");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<OauthPersonalAccessClient>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__oauth_pe__3213E83F3FBA2737");
        });

        modelBuilder.Entity<OauthRefreshToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("oauth_refresh_tokens_id_primary");
        });

        modelBuilder.Entity<PasswordChange>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__password__3213E83FB0749663");
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__permissi__3213E83F16FB7753");

            entity.Property(e => e.AppName).HasDefaultValue("all");
            entity.Property(e => e.HasChild).HasDefaultValue("N");
            entity.Property(e => e.HasRoute).HasDefaultValue("N");
            entity.Property(e => e.IsCrud).HasDefaultValue("N");

            entity.HasMany(d => d.Roles).WithMany(p => p.Permissions)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleHasPermission",
                    r => r.HasOne<Role>().WithMany()
                        .HasForeignKey("RoleId")
                        .HasConstraintName("role_has_permissions_role_id_foreign"),
                    l => l.HasOne<Permission>().WithMany()
                        .HasForeignKey("PermissionId")
                        .HasConstraintName("role_has_permissions_permission_id_foreign"),
                    j =>
                    {
                        j.HasKey("PermissionId", "RoleId").HasName("role_has_permissions_permission_id_role_id_primary");
                        j.ToTable("role_has_permissions");
                        j.IndexerProperty<long>("PermissionId").HasColumnName("permission_id");
                        j.IndexerProperty<long>("RoleId").HasColumnName("role_id");
                    });
        });

        modelBuilder.Entity<PersonalAccessToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__personal__3213E83FEA913443");
        });

        modelBuilder.Entity<PostingPeriod>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__posting___3213E83FC7A876B6");

            entity.Property(e => e.IsLocked).HasDefaultValue("No");
        });

        modelBuilder.Entity<QueueMonitor>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__queue_mo__3213E83FFCB70668");

            entity.Property(e => e.Attempt).HasDefaultValueSql("('0')");
            entity.Property(e => e.Failed).HasDefaultValueSql("('0')");
            entity.Property(e => e.Retried).HasDefaultValueSql("('0')");
            entity.Property(e => e.Status).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<RInv>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__R_Inv__F4D96FAE5E07630D");

            entity.Property(e => e.Deleted)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<RMaster>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__R_Master__F4D96FAE825A50A5");

            entity.Property(e => e.Deleted)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.DocStatus).HasDefaultValue("Open");
            entity.Property(e => e.Status)
                .HasDefaultValue("1")
                .IsFixedLength();
        });

        modelBuilder.Entity<ReleaseNote>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__release___3213E83FA36B553D");

            entity.Property(e => e.Version).HasDefaultValue("v1");
        });

        modelBuilder.Entity<ReleaseNoteReceiver>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__release___3213E83F22B31ED1");
        });

        modelBuilder.Entity<RevisionTransaction>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__revision__3213E83FC0F0926F");

            entity.Property(e => e.Status).HasDefaultValue("open");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__roles__3213E83F1980D3FD");
        });

        modelBuilder.Entity<SerialNumber>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__serial_n__3213E83F36EE554B");

            entity.Property(e => e.IsUsed).HasDefaultValue("no");
        });

        modelBuilder.Entity<SessionLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__session___3213E83FF1365295");
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__settings__3213E83F99979110");
        });

        modelBuilder.Entity<SignLogging>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__sign_log__3213E83F4B579C46");

            entity.Property(e => e.LogType).HasDefaultValue("request");
            entity.Property(e => e.RequestType).HasDefaultValue("SN");
        });

        modelBuilder.Entity<Signature>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Signatur__3213E83F17160038");
        });

        modelBuilder.Entity<TMdoc>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC__F4D96FAE6109D8CD");

            entity.Property(e => e.Container).HasDefaultValueSql("('0')");
            entity.Property(e => e.DecreaseValue).HasDefaultValueSql("('0')");
            entity.Property(e => e.DecreaseValuePph).HasDefaultValueSql("('0')");
            entity.Property(e => e.DecreaseValuePpn).HasDefaultValueSql("('0')");
            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocType).HasDefaultValue("Import");
            entity.Property(e => e.EBillingNo).IsFixedLength();
            entity.Property(e => e.EsignDecimal).HasDefaultValueSql("('3')");
            entity.Property(e => e.ExportClassificationId).IsFixedLength();
            entity.Property(e => e.IncreaseValue).HasDefaultValueSql("('0')");
            entity.Property(e => e.IncreaseValuePph).HasDefaultValueSql("('0')");
            entity.Property(e => e.IncreaseValuePpn).HasDefaultValueSql("('0')");
            entity.Property(e => e.IsChange).HasDefaultValue("N");
            entity.Property(e => e.IsFeOri).HasDefaultValue("N");
            entity.Property(e => e.IsFeSend).HasDefaultValue("N");
            entity.Property(e => e.IsOriginal).HasDefaultValue("N");
            entity.Property(e => e.IsParent).HasDefaultValue("N");
            entity.Property(e => e.IsScan).HasDefaultValue("N");
            entity.Property(e => e.IsSend).HasDefaultValue("N");
            entity.Property(e => e.IsUrgent).HasDefaultValue("N");
        });

        modelBuilder.Entity<TMdoc1>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC1__F4D96FAE540A624D");
        });

        modelBuilder.Entity<TMdocBl>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_b__F4D96FAE634F90F0");

            entity.Property(e => e.CostOfRepair).HasDefaultValueSql("('0')");
            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocType).HasDefaultValue("Import");
            entity.Property(e => e.IsFeOri).HasDefaultValue("N");
            entity.Property(e => e.IsFeSend).HasDefaultValue("N");
            entity.Property(e => e.IsOriginal).HasDefaultValue("N");
            entity.Property(e => e.IsScan).HasDefaultValue("N");
            entity.Property(e => e.IsSend).HasDefaultValue("N");
            entity.Property(e => e.Qty).HasDefaultValueSql("('0')");
            entity.Property(e => e.TransType).HasDefaultValue("Inv");
        });

        modelBuilder.Entity<TMdocBz>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_B__F4D96FAE93DB4B7C");

            entity.Property(e => e.IsSelected).HasDefaultValue("N");
        });

        modelBuilder.Entity<TMdocHeader>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_H__F4D96FAE1DD8D72E");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocStatus).HasDefaultValue("Open");
            entity.Property(e => e.DocType).HasDefaultValue("Import");
            entity.Property(e => e.InvoiceStatus).HasDefaultValue("open");
            entity.Property(e => e.IsChange)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.IsLocked)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.StatusBms).HasDefaultValue("open");
            entity.Property(e => e.TransType).HasDefaultValue("Automatic");
        });

        modelBuilder.Entity<TMdocHeaderBl>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_H__F4D96FAE35137788");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<TMdocHeaderInv>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_H__F4D96FAECECDEED1");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<TMdocHeaderInvSub>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_H__F4D96FAEEFFF78A0");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
        });

        modelBuilder.Entity<TMdocHeaderLocal>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_H__F4D96FAE54CF6473");

            entity.Property(e => e.DocType).HasDefaultValue("Local");
            entity.Property(e => e.IsChange)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.IsLocked)
                .HasDefaultValue("N")
                .IsFixedLength();
            entity.Property(e => e.Status).HasDefaultValue("Open");
            entity.Property(e => e.TransType).HasDefaultValue("Automatic");
        });

        modelBuilder.Entity<TMdocInv>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_i__F4D96FAECC356DFE");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocType).HasDefaultValue("Import");
            entity.Property(e => e.HasSub).HasDefaultValue("N");
            entity.Property(e => e.IsFeOri).HasDefaultValue("N");
            entity.Property(e => e.IsFeSend).HasDefaultValue("N");
            entity.Property(e => e.IsOriginal).HasDefaultValue("N");
            entity.Property(e => e.IsScan).HasDefaultValue("N");
            entity.Property(e => e.IsSend).HasDefaultValue("N");
            entity.Property(e => e.Skbppn).HasDefaultValue("N");
            entity.Property(e => e.TempImport).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<TMdocInvSub>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_i__F4D96FAE82ED468D");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.Type).HasDefaultValue("I");
        });

        modelBuilder.Entity<TMdocLog>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_L__F4D96FAE8AF6ADE9");
        });

        modelBuilder.Entity<TMdocProcess>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_P__F4D96FAEF9C0F74D");
        });

        modelBuilder.Entity<TMdocRef>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_R__F4D96FAEF391732E");
        });

        modelBuilder.Entity<TMdocSign>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_MDOC_S__F4D96FAEF884DFD8");

            entity.Property(e => e.DocType).HasDefaultValue("DynamicSign");
            entity.Property(e => e.PageIndex).HasDefaultValueSql("('1')");
            entity.Property(e => e.Status).HasDefaultValue("draft");
        });

        modelBuilder.Entity<TSapKb>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__T_SAP_KB__F4D96FAEA253BA02");

            entity.Property(e => e.Flags).HasDefaultValueSql("('0')");
            entity.Property(e => e.IsSync).HasDefaultValue("N");
        });

        modelBuilder.Entity<Texp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__TEXP__F4D96FAEA98E0DEA");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocStatus).HasDefaultValue("Open");
        });

        modelBuilder.Entity<Thexp>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__THEXP__F4D96FAE6DF0A2C3");

            entity.Property(e => e.Deleted).HasDefaultValue("N");
            entity.Property(e => e.DocStatus).HasDefaultValue("Open");
            entity.Property(e => e.DocType).HasDefaultValue("Export");
            entity.Property(e => e.InvoiceStatus).HasDefaultValue("open");
            entity.Property(e => e.StatusBms).HasDefaultValue("open");
        });

        modelBuilder.Entity<TimeSheet>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__TimeShee__F4D96FAE96194A2E");

            entity.Property(e => e.LineNum).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__users__3213E83F651E7DE1");

            entity.Property(e => e.AvatarIcon).HasDefaultValue("avatar.png");
            entity.Property(e => e.GetNotification).HasDefaultValue("Y");
            entity.Property(e => e.Manager).HasDefaultValueSql("('0')");
            entity.Property(e => e.ShowNewFeature).HasDefaultValue("N");
            entity.Property(e => e.ShowNotification).HasDefaultValue("Y");
            entity.Property(e => e.Status).HasDefaultValueSql("('0')");
        });

        modelBuilder.Entity<UserDepartment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserDepa__3213E83F0B562A5D");
        });

        modelBuilder.Entity<UserEmail>(entity =>
        {
            entity.HasKey(e => e.DocEntry).HasName("PK__user_ema__F4D96FAEE8D7A9B3");

            entity.Property(e => e.Active)
                .HasDefaultValue("Y")
                .IsFixedLength();
        });

        modelBuilder.Entity<VwBasetableTransaction>(entity =>
        {
            entity.ToView("vw_basetable_transaction");
        });

        modelBuilder.Entity<VwBasetableTransactionV2>(entity =>
        {
            entity.ToView("vw_basetable_transaction_v2");
        });

        modelBuilder.Entity<VwBasetableTransactionV3>(entity =>
        {
            entity.ToView("vw_basetable_transaction_v3");
        });

        modelBuilder.Entity<VwDataImport>(entity =>
        {
            entity.ToView("vw_data_import");
        });

        modelBuilder.Entity<VwDataTemporaryExport>(entity =>
        {
            entity.ToView("vw_data_temporary_export");
        });

        modelBuilder.Entity<VwDocTransactionHeader>(entity =>
        {
            entity.ToView("vw_doc_transaction_header");
        });

        modelBuilder.Entity<VwDocTransactionValue>(entity =>
        {
            entity.ToView("VW_DOC_TRANSACTION_VALUE");
        });

        modelBuilder.Entity<VwDocTransactionValueV2>(entity =>
        {
            entity.ToView("VW_DOC_TRANSACTION_VALUE_V2");
        });

        modelBuilder.Entity<VwDocTransactionValueV3>(entity =>
        {
            entity.ToView("VW_DOC_TRANSACTION_VALUE_V3");
        });

        modelBuilder.Entity<VwLampiranTimbun>(entity =>
        {
            entity.ToView("vw_lampiran_timbun");
        });

        modelBuilder.Entity<VwRecapReexportReimport>(entity =>
        {
            entity.ToView("vw_recap_reexport_reimport");
        });

        modelBuilder.Entity<WebsocketsStatisticsEntry>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__websocke__3213E83F87759CE4");
        });

        modelBuilder.Entity<WorkShift>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__WorkShif__3213E83F39F27DD1");

            entity.Property(e => e.Status).HasDefaultValue("open");
        });

        modelBuilder.Entity<WorkShiftDetail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__WorkShif__3213E83FEF758036");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
