/**
 * QueryNames is an object that holds constant values representing the names of various queries
 * used throughout the application. These constants help in maintaining consistency and avoiding
 * hardcoding query names in multiple places.
 */
export const QueryNames = {
  /**
   * Query to get the list of users.
   */
  GetUsers: 'GetUsers',
  /**
   * Query to get the list of tenants.
   */
  GetTenants: 'GetTenants',
  /**
   * Query to get the list of roles.
   */
  GetRoles: 'GetRoles',
  /**
   * Query to get the application configuration.
   */
  GetAppConfig: 'GetAppConfig',
  /**
   * Query to get the list of features.
   */
  GetFeatures: 'GetFeatures',
  /**
   * Query to get the translations.
   */
  GetTranslations: 'GetTranslations',
  /**
   * Query to get the list of permissions.
   */
  GetPermissions: 'GetPermissions',
  /**
   * Query to get the roles assigned to a user.
   */
  GetUserRoles: 'GetUserRoles',
  /**
   * Query to get the emailing configuration.
   */
  GetEmailing: 'GetEmailing',
  /**
   * Query to get the active directory configuration.
   */
  GetActiveDirectory: 'GetActiveDirectory',
  /**
   * Query to get the list of assignable roles.
   */
  GetAssignableRoles: 'GetAssignableRoles',
  /**
   * Query to get the profile information.
   */
  GetProfile: 'GetProfile',
  /**
   * Query to get the session information.
   */
  GetSession: 'GetSession',
  /**
   * Query to get the list of openiddict applications.
   */
  GetOpeniddictApplications: 'GetOpeniddictApplications',
  GetOpeniddictApplicationsEdit: 'GetOpeniddictApplicationsEdit',
  /**
   * Query to get the list of openiddict applications permissions.
   */
  GetOpeniddictApplicationsPermissions: 'GetOpeniddictApplicationsPermissions',
  /**
   * Query to get the list of openiddict applications resources.
   */
  GetOpeniddictApplicationsResources: 'GetOpeniddictApplicationsResources',
  /**
   * Query to get the list of openiddict grant types.
   */
  GetOpeniddictGrantTypes: 'GetOpeniddictGrantTypes',
  /**
   * Query to get the list of openiddict resources.
   */
  GetOpeniddictResources: 'GetOpeniddictResources',
  /**
   * Query to get the list of openiddict scopes.
   */
  GetOpeniddictScopes: 'GetOpeniddictScopes',
  /**
   * Query to get the list of identity claims.
   */
  GetIdentityClaims: 'GetIdentityClaims',
  /**
   * Query to get the list of identity claim types.
   */
  GetIdentityClaimTypes: 'GetIdentityClaimTypes',
  /**
   * Query to get the list of openiddict resources available resources.
   */
  GetOpeniddictResourcesAvailableResources: 'GetOpeniddictResourcesAvailableResources',
  /**
   * Query to get the list of openiddict scopes available scopes.
   */
  GetOpeniddictScopesAvailableScopes: 'GetOpeniddictScopesAvailableScopes',
  /**
   * Query to get the list of openiddict requirements.
   */
  GetOpeniddictRequirements: 'GetOpeniddictRequirements',
}
