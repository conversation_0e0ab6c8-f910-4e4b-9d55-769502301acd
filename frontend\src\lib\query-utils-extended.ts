import {
  type FilterCondition,
  type FilterGroup,
  type FilterOperator,
  type LogicalOperator,
  type QueryParametersDto,
  type SortInfo,
} from '@/client'

// Define interface for sorting structure
interface SortingItem {
  id: string;
  desc: boolean;
}

/**
 * Interface for parameters needed to generate a query body with multiple filter conditions
 */
export interface ExtendedQueryBuilderParams {
  pageIndex: number
  pageSize: number
  sorting?: string
  filterConditions?: Array<{
    fieldName: string
    operator: FilterOperator
    value: string | null
  }>
}

/**
 * Generates a query parameters object for API list requests with support for multiple filter conditions
 *
 * @param params Query parameters containing pagination, filtering, and sorting info
 * @returns A query parameters object ready to be used in API requests
 */
export function generateExtendedQueryParameters(params: ExtendedQueryBuilderParams): QueryParametersDto {
  const {
    pageIndex,
    pageSize,
    sorting,
    filterConditions = [],
  } = params

  // Skip calculation is handled by the API through the page parameter

  // Ensure Sorting is never empty string
  const sortingValue = sorting ?? ''

  // Create filter group with all conditions
  const filterGroup: FilterGroup = {
    operator: 'And' as LogicalOperator,
    conditions: filterConditions as FilterCondition[],
  }

  // Convert sorting string to SortInfo array
  const sortArray: Array<SortInfo> = []
  if (sorting) {
    try {
      const parsedSorting = JSON.parse(sorting) as SortingItem[]
      parsedSorting.forEach((sort) => {
        sortArray.push({
          field: sort.id,
          desc: sort.desc,
        })
      })
    } catch (e) {
      console.error('Error parsing sorting JSON:', e)
    }
  }

  // Return query parameters object
  return {
    sorting: sortingValue,
    page: pageIndex + 1,
    sort: sortArray,
    filterGroup: filterGroup,
    maxResultCount: pageSize,
  } as QueryParametersDto
}
