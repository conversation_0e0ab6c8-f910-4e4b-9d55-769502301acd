using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Attachments;

/// <summary>
/// Entity for storing attachment metadata
/// </summary>
public class Attachment : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// The name of the file
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// The content type of the file
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// The size of the file in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// The blob name used to store the file in the blob storage
    /// </summary>
    public string? BlobName { get; set; }

    /// <summary>
    /// Optional description of the file
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Optional reference ID that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type that this file is associated with
    /// </summary>
    public string? ReferenceType { get; set; }

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected Attachment()
    {
        // Initialize required properties with empty strings
        FileName = string.Empty;
        ContentType = string.Empty;
        BlobName = string.Empty;
    }

    /// <summary>
    /// Creates a new Attachment
    /// </summary>
    public Attachment(
        Guid id,
        string fileName,
        string contentType,
        long size,
        string blobName,
        string? description = null,
        Guid? referenceId = null,
        string? referenceType = null)
        : base(id)
    {
        FileName = fileName;
        ContentType = contentType;
        Size = size;
        BlobName = blobName;
        Description = description;
        ReferenceId = referenceId;
        ReferenceType = referenceType;
    }
}