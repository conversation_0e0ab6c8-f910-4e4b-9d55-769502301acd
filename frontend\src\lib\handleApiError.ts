/**
 * Type definition for API errors returned from the backend
 */
export interface ApiError {
  details: {
    error: {
      message: string;
      details: string;
    };
  };
}

/**
 * Type guard to check if an error matches our ApiError structure
 * @param error - The error to check
 * @returns True if the error matches the ApiError structure
 */
export const isApiError = (error: unknown): error is ApiError => {
  if (typeof error !== 'object' || error === null) return false;

  // Create a safer type assertion
  const errorObj = error as Record<string, unknown>;

  if (!('details' in errorObj)) return false;
  if (typeof errorObj.details !== 'object' || errorObj.details === null) return false;

  // Check if details has an error property
  const details = errorObj.details as Record<string, unknown>;
  return 'error' in details;
};

/**
 * Helper function to handle API errors and display appropriate toast messages
 * @param err - The error to handle
 * @param toast - The toast function to use for displaying messages
 */
export const handleApiError = (
  err: unknown
) => {
  if (isApiError(err)) {
    return {
      title: err.details.error.message,
      description: err.details.error.details,
      variant: 'error',
    };
  } else {
    // Fallback for unexpected error format
    return {
      title: 'Error',
      description: 'An unexpected error occurred',
      variant: 'error',
    };
  }
};
