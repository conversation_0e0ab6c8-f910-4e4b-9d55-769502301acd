﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Mapping.Extensions;

public static class PaginationMappingExtensions
{
    public static async Task<PagedResultDto<TDestination>> MapPagedResultAsync<TSource, TDestination>(
        this IQueryable<TSource> query,
        PagedAndSortedResultRequestDto input,
        Func<TSource, TDestination> mapper)
    {
        var totalCount = query.Count();

        var items = query
            .OrderByIf(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting)
            .PageBy(input)
            .ToList();

        var mappedItems = items.Select(mapper).ToList();

        return new PagedResultDto<TDestination>(totalCount, mappedItems);
    }

    public static IQueryable<T> PageBy<T>(this IQueryable<T> query, PagedResultRequestDto input)
    {
        return query.Skip(input.SkipCount).Take(input.MaxResultCount);
    }
}
