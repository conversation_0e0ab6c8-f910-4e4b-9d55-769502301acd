using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Imip.Ekb.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Security.Claims;

namespace Imip.Ekb.Web.Services;

public static class AuthenticationConfigurationService
{
    /// <summary>
    /// Configures authentication services including Cookies, JWT Bearer, and OpenID Connect
    /// </summary>
    public static void ConfigureAuthentication(IServiceCollection services, IConfiguration configuration)
    {
        var authBuilder = services.AddAuthentication(options =>
        {
            options.DefaultScheme = "Cookies";
            options.DefaultChallengeScheme = "Cookies";
            options.DefaultSignOutScheme = "Cookies";
        });

        ConfigureCookieAuthentication(authBuilder);
        ConfigureJwtBearerAuthentication(authBuilder, configuration);
        // ConfigureOpenIdConnectAuthentication(authBuilder, configuration);
        ConfigureAbpClaimsOptions(services);
        ConfigureJwtBearerOptions(services, configuration);

        // Register HttpContextAccessor for the custom authentication scheme provider
        services.AddHttpContextAccessor();

        // Configure policy selector to use Bearer for API endpoints
        services.AddSingleton<IAuthenticationSchemeProvider, ApiAwareAuthenticationSchemeProvider>();
    }

    private static void ConfigureCookieAuthentication(AuthenticationBuilder authBuilder)
    {
        authBuilder.AddCookie("Cookies", options =>
        {
            options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
            options.SlidingExpiration = true;
            options.Cookie.Name = ".Imip.Ekb.Auth";
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";

            // Add debugging for cookie events
            options.Events.OnRedirectToLogin = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                logger.LogDebug("Cookie authentication redirecting to login: {LoginPath}", context.RedirectUri);

                // Allow the default redirect behavior to occur
                context.Response.Redirect(context.RedirectUri);
                return Task.CompletedTask;
            };

            options.Events.OnRedirectToAccessDenied = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                logger.LogWarning("Cookie authentication access denied for path: {Path}", context.Request.Path);
                return Task.CompletedTask;
            };
        });
    }

    private static void ConfigureJwtBearerAuthentication(AuthenticationBuilder authBuilder, IConfiguration configuration)
    {
        authBuilder.AddJwtBearer("Bearer", options =>
        {
            // Configure JWT Bearer for validating tokens from other apps
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);

            // Configure token validation parameters
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = configuration["AuthServer:Authority"],
                // Accept both "IdentityServer" and the local client ID as valid audiences
                ValidAudiences = new[] { "IdentityServer", configuration["AuthServer:ClientId"], "Ekb" },
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            // Handle JWT events for debugging and custom validation
            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = async context =>
                {
                    var tokenValidationService = context.HttpContext.RequestServices
                        .GetRequiredService<IAuthenticationTokenValidationService>();
                    await tokenValidationService.ValidateJwtBearerTokenAsync(context);
                },
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                    logger.LogError("JWT authentication failed: {Exception}", context.Exception);
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                    logger.LogDebug("JWT authentication challenge initiated for path: {Path}", context.Request.Path);

                    // Skip the default challenge behavior to prevent redirects
                    context.HandleResponse();

                    // Return JSON error response for API endpoints
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "application/json";

                    var errorResponse = new
                    {
                        error = new
                        {
                            code = "UNAUTHORIZED",
                            message = "Authentication failed",
                            details = "Bearer token is required for API access"
                        },
                        timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                        path = context.Request.Path.Value
                    };

                    var jsonOptions = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        WriteIndented = true
                    };

                    return context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse, jsonOptions));
                }
            };
        });
    }

    private static void ConfigureOpenIdConnectAuthentication(AuthenticationBuilder authBuilder, IConfiguration configuration)
    {
        authBuilder.AddOpenIdConnect("oidc", options =>
        {
            ConfigureOpenIdConnectBasicSettings(options, configuration);
            ConfigureOpenIdConnectScopes(options);
            ConfigureOpenIdConnectClaims(options);
            ConfigureOpenIdConnectEvents(options);
        });
    }

    private static void ConfigureOpenIdConnectBasicSettings(OpenIdConnectOptions options, IConfiguration configuration)
    {
        // Use consistent configuration keys - pick one set
        options.Authority = configuration["AuthServer:Authority"] ?? configuration["OpenIdConnect:Authority"];
        options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);

        options.ClientId = configuration["AuthServer:ClientId"] ?? configuration["OpenIdConnect:ClientId"];
        options.ClientSecret = configuration["AuthServer:ClientSecret"] ?? configuration["OpenIdConnect:ClientSecret"];

        // Configure redirect URIs
        options.SignedOutRedirectUri = configuration["OpenIdConnect:SignedOutRedirectUri"] ?? "http://localhost:5000";
        options.SignedOutCallbackPath = "/signout-callback-oidc";
        options.RemoteSignOutPath = "/signout-oidc";

        // Configure sign-in scheme to use cookies
        options.SignInScheme = "Cookies";

        // Use authorization code flow with PKCE for security
        options.ResponseType = "code";
        options.UsePkce = false; // Disable PKCE for HTTP behind HTTPS proxy

        // Save tokens and get claims from UserInfo
        options.SaveTokens = true;
        options.GetClaimsFromUserInfoEndpoint = true;

        // Configure token refresh
        options.RefreshInterval = TimeSpan.FromMinutes(30); // Refresh every 30 minutes
        options.UseTokenLifetime = true; // Use token lifetime from identity server
    }

    private static void ConfigureOpenIdConnectScopes(OpenIdConnectOptions options)
    {
        // Basic scopes
        options.Scope.Clear();
        options.Scope.Add("openid");
        options.Scope.Add("profile");
        options.Scope.Add("email");
    }

    private static void ConfigureOpenIdConnectClaims(OpenIdConnectOptions options)
    {
        // Map claims properly
        options.ClaimActions.MapJsonKey(ClaimTypes.NameIdentifier, "sub");
        options.ClaimActions.MapJsonKey(ClaimTypes.Name, "name");
        options.ClaimActions.MapJsonKey(ClaimTypes.Email, "email");
        options.ClaimActions.MapJsonKey(ClaimTypes.Role, "role");
    }

    private static void ConfigureOpenIdConnectEvents(OpenIdConnectOptions options)
    {
        // Handle token validation
        options.Events.OnTokenValidated = async context =>
        {
            var tokenValidationService = context.HttpContext.RequestServices
                .GetRequiredService<IAuthenticationTokenValidationService>();
            await tokenValidationService.ValidateOpenIdConnectTokenAsync(context);
        };

        // Handle sign-in errors
        options.Events.OnRemoteFailure = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogError("Remote authentication failure: {Error}", context.Failure?.Message);

            // Check if this is a data protection error
            if (context.Failure?.Message?.Contains("Unable to unprotect") == true ||
                context.Failure?.Message?.Contains("key was not found") == true)
            {
                logger.LogWarning("Detected data protection error, clearing problematic cookies");

                // Clear problematic cookies
                context.Response.Cookies.Delete(".Imip.Ekb.Auth");
                context.Response.Cookies.Delete(".Imip.Ekb.Antiforgery");
                context.Response.Cookies.Delete(".Imip.Ekb.Session");

                // Redirect to login with a clean slate
                context.Response.Redirect("/Account/Login?error=session_expired");
                context.HandleResponse();
                return Task.CompletedTask;
            }

            context.Response.Redirect("/Account/Login?error=" + Uri.EscapeDataString(context.Failure?.Message ?? "Authentication failed"));
            context.HandleResponse();
            return Task.CompletedTask;
        };

        // Handle tenant information in redirect (if using multi-tenancy)
        options.Events.OnRedirectToIdentityProvider = context =>
        {
            var currentTenant = context.HttpContext.RequestServices.GetRequiredService<ICurrentTenant>();
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            var configuration = context.HttpContext.RequestServices.GetRequiredService<IConfiguration>();

            // Workaround: Force HTTPS scheme for external domains when behind proxy
            var appUrl = configuration["App:SelfUrl"] ?? configuration["App:ServerRootAddress"];
            var forceHttpsDomains = configuration.GetSection("OpenIdConnect:ForceHttpsDomains").Get<string[]>() ?? new string[0];

            if (!string.IsNullOrEmpty(appUrl))
            {
                try
                {
                    var appUri = new Uri(appUrl);
                    var appHost = appUri.Host;

                    if (context.Request.Host.Value.Contains(appHost) && context.Request.Scheme == "http")
                    {
                        logger.LogWarning("Detected HTTP scheme for external domain {AppHost}, forcing HTTPS for redirect URI", appHost);
                        var httpsRedirectUri = context.ProtocolMessage.RedirectUri.Replace("http://", "https://");
                        context.ProtocolMessage.RedirectUri = httpsRedirectUri;
                        logger.LogWarning("Updated RedirectUri to: {UpdatedRedirectUri}", httpsRedirectUri);
                    }
                }
                catch (UriFormatException ex)
                {
                    logger.LogWarning("Invalid App URL format: {AppUrl}, error: {Error}", appUrl, ex.Message);
                }
            }

            // Additional workaround: Force HTTPS for configured domains
            var domainsToCheck = forceHttpsDomains.Any() ? forceHttpsDomains : new[] { "ekb-dev.imip.co.id", "ekb.imip.co.id" };

            if (domainsToCheck.Any(domain => context.Request.Host.Value.Contains(domain)) &&
                context.ProtocolMessage.RedirectUri.StartsWith("http://"))
            {
                logger.LogWarning("Forcing HTTPS for external domain redirect URI: {OriginalUri}", context.ProtocolMessage.RedirectUri);
                context.ProtocolMessage.RedirectUri = context.ProtocolMessage.RedirectUri.Replace("http://", "https://");
                logger.LogWarning("Final RedirectUri: {FinalUri}", context.ProtocolMessage.RedirectUri);
            }

            if (currentTenant.Id.HasValue && !string.IsNullOrEmpty(currentTenant.Name))
            {
                context.ProtocolMessage.SetParameter("tenant", currentTenant.Name);
            }

            return Task.CompletedTask;
        };

        // Handle authentication errors
        options.Events.OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogError(context.Exception, "OIDC authentication failed");
            return Task.CompletedTask;
        };

        ConfigureOpenIdConnectDebuggingEvents(options);
    }

    private static void ConfigureOpenIdConnectDebuggingEvents(OpenIdConnectOptions options)
    {
        // Ensure user is signed in to the cookie scheme
        options.Events.OnTicketReceived = async context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();

            // Log authentication details
            if (context.Principal?.Identity?.IsAuthenticated == true)
            {
                // logger.LogInformation("User is authenticated: {UserName}", context.Principal.Identity.Name);
                // logger.LogInformation("Authentication type: {AuthType}", context.Principal.Identity.AuthenticationType);

                // Log all claims for debugging
                foreach (var claim in context.Principal.Claims)
                {
                    logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
                }

                // logger.LogInformation("OIDC authentication completed successfully");
            }
            else
            {
                logger.LogWarning("User is not authenticated after ticket received");
            }
        };

        // Add more event handlers for debugging
        options.Events.OnAuthorizationCodeReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            // logger.LogInformation("Authorization code received");
            return Task.CompletedTask;
        };

        options.Events.OnTokenResponseReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            // logger.LogInformation("Token response received");
            return Task.CompletedTask;
        };

        options.Events.OnUserInformationReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            // logger.LogInformation("User information received");
            return Task.CompletedTask;
        };
    }

    private static void ConfigureAbpClaimsOptions(IServiceCollection services)
    {
        services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });
    }

    private static void ConfigureJwtBearerOptions(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<JwtBearerOptions>(options =>
        {
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
            // Configure token validation to accept multiple audiences
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidAudiences = new[] { "IdentityServer", configuration["AuthServer:ClientId"], "Ekb" }
            };
        });
    }
}