import { client } from '@/client/client.gen';
import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { createRoot } from "react-dom/client";
import './index.css';
import './theme.css';

// Declare global variable for TypeScript
declare global {
  interface Window {
    __RequestVerificationToken?: string;
  }
}

const appName = window.document.getElementsByTagName("title")[0]?.innerText || "Inertia";
// Custom fetch function with desired options
const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  return fetch(input, {
    credentials: 'include',
    duplex: 'half',
    ...init,
  } as RequestInit);
};

client.interceptors.request.use((request) => {
  const csrfToken = window.__RequestVerificationToken;
  request.headers.append("X-CSRF-TOKEN", csrfToken as string)
  request.headers.append("X-Requested-With", "XMLHttpRequest")
  return request
})

client.setConfig({
  throwOnError: true,
  baseUrl: "/",
  fetch: customFetch
})

createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: (name) => resolvePageComponent(`./pages/${name}.tsx`, import.meta.glob("./pages/**/*.tsx")),
  setup({ el, App, props }) {
    const root = createRoot(el);
    root.render(<App {...props} />);
  },
  progress: {
    color: '#4B5563',
  },
}).catch(console.error); 