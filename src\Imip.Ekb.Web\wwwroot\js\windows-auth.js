/**
 * Windows Authentication Helper
 * 
 * This script helps detect if a user is already authenticated in Windows
 * and can pass that information to the server.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the Windows Authentication button
    const adLoginButton = document.getElementById('adLoginButton');
    
    if (adLoginButton) {
        adLoginButton.addEventListener('click', function(e) {
            // We'll attempt to detect Windows authentication
            // This is a simplified approach - in a real implementation,
            // you might use more sophisticated techniques
            
            // For demonstration, we'll set a cookie that the server can read
            // In a real implementation, you might use SPNEGO/Kerberos or other methods
            
            // Get the current domain username if available
            let windowsUser = '';
            
            // Try to get from browser environment
            // Note: This is not reliable and will only work in specific environments
            if (typeof window.external !== 'undefined' && 
                typeof window.external.GetUserInfo === 'function') {
                try {
                    windowsUser = window.external.GetUserInfo();
                } catch (e) {
                    console.log('Could not get Windows user info');
                }
            }
            
            // Set a cookie with the Windows username (if available)
            if (windowsUser) {
                document.cookie = `WindowsAuthUser=${windowsUser}; path=/; secure; samesite=strict`;
            }
            
            // Continue with form submission
            // The server-side code will try to detect the Windows identity
        });
    }
});
