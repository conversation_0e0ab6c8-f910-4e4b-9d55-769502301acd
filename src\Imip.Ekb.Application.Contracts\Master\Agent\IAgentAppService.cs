using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Agent.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Agent;

public interface IAgentAppService :
    ICrudAppService<AgentDto, Guid, PagedAndSortedResultRequestDto, AgentCreateUpdateDto, AgentCreateUpdateDto>
{
    Task<PagedResultDto<AgentDto>> FilterListAsync(QueryParametersDto parameters);
}