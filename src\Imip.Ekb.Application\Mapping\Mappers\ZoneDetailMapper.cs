using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ZoneDetailMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ZoneDetail.Id), nameof(ZoneDetailDto.Id))]
    public partial ZoneDetailDto MapToDto(ZoneDetail entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapProperty(nameof(ZoneDetailCreateUpdateDto.Id), nameof(ZoneDetail.Id))]
    [MapperIgnoreSource(nameof(ZoneDetailCreateUpdateDto.Id))] // Don't overwrite existing Id
    [MapperIgnoreTarget(nameof(ZoneDetail.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ZoneDetail.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(ZoneDetail.CreatedAt))]
    [MapperIgnoreTarget(nameof(ZoneDetail.CreatedId))]
    [MapperIgnoreTarget(nameof(ZoneDetail.DeletedAt))] // Don't overwrite soft delete fields
    [MapperIgnoreTarget(nameof(ZoneDetail.DeleteBy))]
    public partial void MapToEntity(ZoneDetailCreateUpdateDto dto, ZoneDetail entity);

    // Custom mapping methods for complex scenarios
    public ZoneDetail CreateEntityWithId(ZoneDetailCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ZoneDetail)Activator.CreateInstance(typeof(ZoneDetail), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ZoneDetailDto> MapToDtoList(List<ZoneDetail> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ZoneDetailDto> MapToDtoEnumerable(IEnumerable<ZoneDetail> entities);
}