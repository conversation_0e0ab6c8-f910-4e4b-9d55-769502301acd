using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Attachments
{
    /// <summary>
    /// Entity for tracking temporary ZIP files that need to be deleted
    /// </summary>
    public class TemporaryZipFile : CreationAuditedEntity<Guid>
    {
        /// <summary>
        /// The name of the ZIP file in the blob storage
        /// </summary>
        public string? BlobName { get; set; }

        /// <summary>
        /// The time when the ZIP file should be deleted
        /// </summary>
        public DateTime DeleteAfter { get; set; }

        /// <summary>
        /// Whether the file has been processed (deleted)
        /// </summary>
        public bool IsProcessed { get; set; }

        /// <summary>
        /// Optional description or additional data
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        protected TemporaryZipFile()
        {
        }

        /// <summary>
        /// Creates a new TemporaryZipFile
        /// </summary>
        public TemporaryZipFile(
            Guid id,
            string blobName,
            DateTime deleteAfter)
            : base(id)
        {
            BlobName = blobName;
            DeleteAfter = deleteAfter;
            IsProcessed = false;
        }
    }
}
