using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.BcType.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.BcType;

[Authorize]
public class BcTypeAppService :
    CrudAppService<BcType, BcTypeDto, Guid, PagedAndSortedResultRequestDto, BcTypeCreateUpdateDto, BcTypeCreateUpdateDto>,
    IBcTypeAppService
{
    private readonly IBcTypeRepository _bcTypeRepository;
    private readonly BcTypeMapper _mapper;
    private readonly ILogger<BcTypeAppService> _logger;

    public BcTypeAppService(
        IBcTypeRepository bcTypeRepository,
        BcTypeMapper mapper,
        ILogger<BcTypeAppService> logger)
        : base(bcTypeRepository)
    {
        _bcTypeRepository = bcTypeRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<BcTypeDto> CreateAsync(BcTypeCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _bcTypeRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<BcTypeDto> UpdateAsync(Guid id, BcTypeCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _bcTypeRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _bcTypeRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _bcTypeRepository.GetAsync(id);
        entity.Status = "Deleted";
        entity.UpdatedAt = Clock.Now;
        await _bcTypeRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<BcTypeDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _bcTypeRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<BcTypeDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _bcTypeRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(BcType.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<BcTypeDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<BcTypeDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _bcTypeRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<BcTypeDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<BcType> ApplyDynamicQuery(IQueryable<BcType> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<BcType>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<BcType>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}