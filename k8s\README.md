﻿# Kubernetes Deployment for Imip.Ekb

This directory contains Kubernetes configuration files for deploying the Imip.Ekb application to Kubernetes clusters.

## Directory Structure

- `dev/`: Contains configuration files for the development environment
- `prod/`: Contains configuration files for the production environment
- `namespaces.yaml`: Defines the Kubernetes namespaces for both environments

## Configuration Files

Each environment directory contains:

1. `configmap.yaml`: Environment-specific configuration
2. `secrets.yaml`: Sensitive configuration data (connection strings, passwords, etc.)
3. `web-deployment.yaml`: Deployment configuration for the web application
4. `web-service.yaml`: Service configuration for the web application
5. `db-migrator-job.yaml`: Job configuration for the database migrator
6. `ingress.yaml`: Ingress configuration for external access

## Prerequisites

Before deploying, you need to:

1. Update the domain names in the ingress configurations
2. Update the database connection strings in the secrets files
3. Set up a Kubernetes cluster with:
   - NGINX Ingress Controller
   - cert-manager for TLS certificates
   - A default StorageClass for persistent volumes

## Manual Deployment

To manually deploy to the development environment:

```bash
# Create namespaces
kubectl apply -f namespaces.yaml

# Apply ConfigMap and Secrets
kubectl apply -f dev/configmap.yaml
kubectl apply -f dev/secrets.yaml

# Run database migrations
kubectl apply -f dev/db-migrator-job.yaml

# Deploy web application
kubectl apply -f dev/web-deployment.yaml
kubectl apply -f dev/web-service.yaml
kubectl apply -f dev/ingress.yaml
```

For production, replace `dev/` with `prod/`.

## GitLab CI/CD

The repository includes a `.gitlab-ci.yml` file that automates the deployment process:

- Pushing to the `dev` branch triggers deployment to the development environment
- Pushing to the `main` branch allows manual deployment to the production environment

### Required GitLab CI/CD Variables

Set the following variables in your GitLab project settings:

- `CI_REGISTRY`, `CI_REGISTRY_USER`, `CI_REGISTRY_PASSWORD`: GitLab container registry credentials
- `KUBE_CONFIG`: Base64-encoded Kubernetes configuration file with access to both clusters

## Customization

You may need to customize these files based on your specific requirements:

- Adjust resource limits in deployment files
- Update the ingress configuration based on your ingress controller
- Modify the database connection strings for your database servers

## Certificate Configuration

The application requires an OpenIddict certificate for authentication. If you're experiencing certificate issues, follow these steps:

1. Make sure your GitLab CI/CD pipeline has run successfully to deploy the application with the correct certificate configuration.

2. Verify that the certificate secret exists:

```bash
kubectl get secret imip-ekb-certificate -n imip-ekb-dev
```

3. Restart the deployment:

```bash
kubectl rollout restart deployment imip-ekb-web -n imip-ekb-dev
```

### Troubleshooting Certificate Issues

If you're still experiencing certificate issues:

1. Make sure the certificate secret contains the correct certificate file:

```bash
kubectl describe secret imip-ekb-certificate -n imip-ekb-dev
```

2. Check if the certificate is correctly mounted in the pod:

```bash
kubectl exec -it -n imip-ekb-dev deployment/imip-ekb-web -- ls -la /app/certs
```

3. If needed, manually create a symbolic link in the pod:

```bash
kubectl exec -it -n imip-ekb-dev deployment/imip-ekb-web -- ln -sf /app/certs/identity-server.pfx /app/certs/openiddict.pfx
```
