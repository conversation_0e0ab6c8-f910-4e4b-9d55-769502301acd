import {
  IconAppWindow,
  IconDashboard,
  IconDatabase,
  IconKey,
  IconSettings,
  IconUser,
  IconUsersGroup,
} from '@tabler/icons-react'
import type { Policy } from '@/lib/hooks/useGrantedPolicies'

export const siteConfig = {
  name: 'Identity Provider',
  baseLinks: {
    login: '/auth/login',
  },
}

export type siteConfig = typeof siteConfig

// export const clientConfig = {
//   url: process.env.NEXT_PUBLIC_API_URL,
//   audience: process.env.NEXT_PUBLIC_API_URL,
//   client_id: process.env.NEXT_PUBLIC_CLIENT_ID,
//   client_secret: process.env.NEXT_PUBLIC_CLIENT_SECRET,
//   scope: process.env.NEXT_PUBLIC_SCOPE,
//   redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/auth/openiddict`,
//   post_logout_redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}`,
//   response_type: 'code',
//   grant_type: 'authorization_code',
//   post_login_route: `${process.env.NEXT_PUBLIC_APP_URL}/admin`,
//   code_challenge_method: 'S256',
// }

/**
 * List of menus shown in the Admin layout.
 * Each menu item contains a title, url, icon, and optional permission.
 */
export const AdminMenus: Array<{
  title: string;
  url: string;
  isActive: boolean;
  icon: React.ComponentType;
  items?: Array<{
    title: string;
    url: string;
    permission: Policy;
  }>,
  permission?: Policy;
}> = [
    {
      title: 'Dashboard',
      url: '/admin',
      isActive: false,
      icon: IconDashboard,
    },
    {
      title: 'Claim Types',
      url: '/admin/claims',
      isActive: false,
      icon: IconKey,
      permission: 'IdentityServer.ClaimTypes',
    },
    {
      title: 'Users',
      url: '/admin/users',
      isActive: false,
      icon: IconUser,
      permission: 'AbpIdentity.Users',
    },
    {
      title: 'Roles',
      url: '/admin/users/roles',
      isActive: false,
      icon: IconUsersGroup,
      permission: 'AbpIdentity.Roles',
    },
    {
      title: 'Clients',
      url: '#',
      isActive: false,
      icon: IconAppWindow,
      permission: 'IdentityServer.OpenIddictApplications',
      items: [
        {
          title: 'Clients',
          url: '/admin/clients',
          permission: 'IdentityServer.OpenIddictApplications',
        },
        // {
        //   title: 'Grant Types',
        //   url: '/admin/clients/grant-types',
        //   active: false
        // },
        {
          title: 'Resources',
          url: '/admin/clients/resources',
          permission: 'IdentityServer.OpenIddictResources',
        },
        {
          title: 'Scopes',
          url: '/admin/clients/scopes',
          permission: 'IdentityServer.OpenIddictScopes',
        },
      ],
    },
    {
      title: 'Tenants',
      url: '/admin/tenants',
      isActive: false,
      icon: IconDatabase,
      permission: 'AbpTenantManagement.Tenants',
    },
    {
      title: 'Settings',
      url: '/admin/settings',
      isActive: false,
      icon: IconSettings,
      permission: 'SettingManagement.Emailing',
    },
  ]
