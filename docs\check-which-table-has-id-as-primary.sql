DECLARE @Tables TABLE (
    SchemaName NVARCHAR(128),
    TableName NVARCHAR(128)
);

-- Step 1: List your tables here (schema + table)
INSERT INTO @Tables (SchemaName, TableName)
VALUES 
    ('dbo', 'M_Signer'),
    ('dbo', 'M_Letter'),
    ('dbo', 'MasterRateUsdConvert'),
    ('dbo', 'MPLAT'),
    ('dbo', 'AppChangeLog'),
    ('dbo', 'M_Currency'),
    ('dbo', 'MasterTenantDocValid'),
    ('dbo', 'M_PPJK'),
    ('dbo', 'BHEXP'),
    ('dbo', 'settings'),
    ('dbo', 'MasterGroup'),
    ('dbo', 'BEXP'),
    ('dbo', 'batch_approvals'),
    ('dbo', 'MasterWarehouse'),
    ('dbo', 'BHIMP'),
    ('dbo', 'TimeSheet'),
    ('dbo', 'BIMP'),
    ('dbo', 'MonitorDocWhs'),
    ('dbo', 'M_Agent'),
    ('dbo', 'M_Tugboat'),
    ('dbo', 'M_CARGO'),
    ('dbo', 'M_TBC'),
    ('dbo', 'release_notes'),
    ('dbo', 'M_DestinationPort'),
    ('dbo', 'M_Tongkang'),
    ('dbo', 'M_BP'),
    ('dbo', 'M_PortOfLoading'),
    ('dbo', 'doc_check_errors'),
    ('dbo', 'M_Jetty'),
    ('dbo', 'O_Roles'),
    ('dbo', 'T_SAP_KB'),
    ('dbo', 'doc_check_error_headers'),
    ('dbo', 'O_Permissions'),
    ('dbo', 'M_LocalItem'),
    ('dbo', 'M_MT'),
    ('dbo', 'L_ProcessDoc'),
    ('dbo', 'M_Classification'),
    ('dbo', 'master_surveyors'),
    ('dbo', 'L_MDOC'),
    ('dbo', 'master_tradings'),
    ('dbo', 'BEXP1'),
    ('dbo', 'M_User_tenant'),
    ('dbo', 'T_MDOC_Sign'),
    ('dbo', 'M_Category_item'),
    ('dbo', 'BIMP1'),
    ('dbo', 'M_Tenant_letter'),
    ('dbo', 'DocSignCoordinate'),
    ('dbo', 'MPS'),
    ('dbo', 'DocumentApproval'),
    ('dbo', 'T_MDOC_Header_inv_sub'),
    ('dbo', 'M_Remark'),
    ('dbo', 'MasterTempImport'),
    ('dbo', 'MPLPS'),
    ('dbo', 'T_MDOC_inv_sub'),
    ('dbo', 'M_Doc_attachment'),
    ('dbo', 'MPLTENANT'),
    ('dbo', 'form_cancels'),
    ('dbo', 'M_DisplayForm'),
    ('dbo', 'MPLSL'),
    ('dbo', 'form_cancel_items'),
    ('dbo', 'M_UserDisplayForm'),
    ('dbo', 'api_logs'),
    ('dbo', 'M_Country'),
    ('dbo', 'L_master'),
    ('dbo', 'M_Representative'),
    ('dbo', 'M_Trans_Type'),
    ('dbo', 'M_CatClassification'),
    ('dbo', 'ExportClassification'),
    ('dbo', 'WorkShifts'),
    ('dbo', 'BHLOCAL'),
    ('dbo', 'WorkShiftDetails'),
    ('dbo', 'sign_loggings'),
    ('dbo', 'revision_transactions'),
    ('dbo', 'T_MDOC1'),
    ('dbo', 'T_MDOC_Header'),
    ('dbo', 'T_MDOC_Header_bl'),
    ('dbo', 'R_Master'),
    ('dbo', 'T_MDOC_bl'),
    ('dbo', 'Signature'),
    ('dbo', 'R_Inv'),
    ('dbo', 'T_MDOC_Header_inv'),
    ('dbo', 'EmployeeWorkShifts'),
    ('dbo', 'notification_db'),
    ('dbo', 'T_MDOC_inv'),
    ('dbo', 'Departments'),
    ('dbo', 'notuls'),
    ('dbo', 'm_catalog_detail'),
    ('dbo', 'UserDepartment'),
    ('dbo', 'T_MDOC_Ref'),
    ('dbo', 'notul_headers'),
    ('dbo', 'T_MDOC_BZ'),
    ('dbo', 'posting_periods'),
    ('dbo', 'M_Tenant_Document_Config'),
    ('dbo', 'T_MDOC_Process'),
    ('dbo', 'T_MDOC_Logs'),
    ('dbo', 'DocQtyInv'),
    ('dbo', 'serial_numbers'),
    ('dbo', 'user_emails'),
    ('dbo', 'M_UserPpjk'),
    ('dbo', 'MasterHsCode'),
    ('dbo', 'Notifications'),
    ('dbo', 'activity_log'),
    ('dbo', 'hs_code_types'),
    ('dbo', 'M_Catalog'),
    ('dbo', 'FormStatement'),
    ('dbo', 'FormStatementHeader'),
    ('dbo', 'L_esign'),
    ('dbo', 'catalog_imgs'),
    ('dbo', 'MasterHsCodeName'),
    ('dbo', 'T_MDOC_Header_Local'),
    ('dbo', 'DocumentApprovalItem'),
    ('dbo', 'M_ExchangeRate'),
    ('dbo', 'AssisTug'),
    ('dbo', 'L_process_esign'),
    ('dbo', 'THEXP'),
    ('dbo', 'TEXP');

    SELECT
    t.SchemaName,
    t.TableName,
    c.COLUMN_NAME AS PrimaryKeyColumn
FROM
    @Tables t
    JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        ON tc.TABLE_SCHEMA = t.SchemaName
        AND tc.TABLE_NAME = t.TableName
        AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE c
        ON c.TABLE_SCHEMA = t.SchemaName
        AND c.TABLE_NAME = t.TableName
        AND c.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
WHERE
    c.COLUMN_NAME = 'Id'
ORDER BY
    t.SchemaName, t.TableName;


SchemaName	TableName	PrimaryKeyColumn
dbo	activity_log	id
dbo	api_logs	id
dbo	batch_approvals	id
dbo	catalog_imgs	id
dbo	Departments	id
dbo	doc_check_error_headers	id
dbo	doc_check_errors	id
dbo	DocumentApproval	id
dbo	DocumentApprovalItem	id
dbo	EmployeeWorkShifts	id
dbo	ExportClassification	Id
dbo	form_cancel_items	id
dbo	form_cancels	id
dbo	hs_code_types	id
dbo	master_surveyors	id
dbo	master_tradings	id
dbo	MasterRateUsdConvert	Id
dbo	MasterTenantDocValid	Id
dbo	notification_db	id
dbo	notul_headers	id
dbo	notuls	id
dbo	posting_periods	id
dbo	release_notes	id
dbo	revision_transactions	id
dbo	serial_numbers	id
dbo	settings	id
dbo	sign_loggings	id
dbo	Signature	id
dbo	UserDepartment	id
dbo	WorkShiftDetails	id
dbo	WorkShifts	id