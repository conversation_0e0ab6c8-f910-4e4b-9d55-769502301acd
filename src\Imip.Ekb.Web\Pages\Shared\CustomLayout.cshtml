@using System.Globalization
<!DOCTYPE html>
<html lang="@CultureInfo.CurrentCulture.Name">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="IMIP Identity Provider">
    <title>@ViewData["Title"]</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Base Styles -->
    @* <link rel="stylesheet" href="/css/base.css"> *@

    <!-- Render any additional styles from child pages -->
    @await RenderSectionAsync("styles", required: false)
</head>

<body>
    <!-- Main Content -->
    <main>
        @RenderBody()
    </main>

    <!-- Render any additional scripts from child pages -->
    @await RenderSectionAsync("scripts", required: false)
</body>

</html>