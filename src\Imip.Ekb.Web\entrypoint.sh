#!/bin/bash
set -e

# Set environment
echo "Setting ASPNETCORE_ENVIRONMENT to ${ASPNETCORE_ENVIRONMENT:-Production}"
export ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}

# Check for certificates
if [ -d "/app/certs" ]; then
    echo "Certificate directory contents:"
    ls -la /app/certs

    if [ -f "/app/certs/identity-server.pfx" ]; then
        echo "Found certificate at /app/certs/identity-server.pfx"
    else
        echo "WARNING: Certificate not found at /app/certs/identity-server.pfx"
        echo "This will cause the application to fail"
    fi
else
    echo "WARNING: Certificate directory /app/certs does not exist"
    echo "This will cause the application to fail"
fi

# Print environment for debugging
echo "Current environment: $ASPNETCORE_ENVIRONMENT"
echo "Current directory: $(pwd)"
echo "Directory listing: $(ls -la)"

# Start the application
echo "Starting application..."
exec dotnet Imip.Ekb.Web.dll
