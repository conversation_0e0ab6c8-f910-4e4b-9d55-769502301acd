using AutoMapper;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;

namespace Imip.Ekb;

public class EkbApplicationAutoMapperProfile : Profile
{
    public EkbApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */

        CreateMap<ZoneDetail, ZoneDetailDto>().ReverseMap();
        CreateMap<ZoneDetail, ZoneDetailCreateUpdateDto>().ReverseMap();
    }
}
