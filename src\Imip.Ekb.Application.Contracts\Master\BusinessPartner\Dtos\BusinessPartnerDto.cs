using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.BusinessPartner.Dtos;

public class BusinessPartnerDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Alias { get; set; }
    public string? Image { get; set; }
    public string? Direction { get; set; }
    public string RegionType { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Tenant { get; set; }
    public string? Npwp1 { get; set; }
    public string? Npwp2 { get; set; }
}