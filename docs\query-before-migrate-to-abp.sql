-- update sql server COMPATIBILITY_LEVEL
ALTER DATABASE EKB_PRD  
SET COMPATIBILITY_LEVEL = 150;

ALTER DATABASE EKB_DEV  
SET COMPATIBILITY_LEVEL = 150;

-- step 1
ALTER TABLE T_MDOC
DROP COLUMN id;

ALTER TABLE dbo.DocumentApprovalItem
DROP CONSTRAINT documentapprovalitem_document_approval_id_foreign;

-----------------------------------------------------------------------------

-- step 1

DECLARE @Tables TABLE (
    SchemaName NVARCHAR(128),
    TableName NVARCHAR(128)
);

-- Step 1: List your tables here (schema + table)
INSERT INTO @Tables (SchemaName, TableName)
VALUES 
    ('dbo', 'M_Signer'),
    ('dbo', 'M_Letter'),
    ('dbo', 'MasterRateUsdConvert'),
    ('dbo', 'MPLAT'),
    ('dbo', 'AppChangeLog'),
    ('dbo', 'M_Currency'),
    ('dbo', 'MasterTenantDocValid'),
    ('dbo', 'M_PPJK'),
    ('dbo', 'BHEXP'),
    ('dbo', 'settings'),
    ('dbo', 'MasterGroup'),
    ('dbo', 'BEXP'),
    ('dbo', 'batch_approvals'),
    ('dbo', 'MasterWarehouse'),
    ('dbo', 'BHIMP'),
    ('dbo', 'TimeSheet'),
    ('dbo', 'BIMP'),
    ('dbo', 'MonitorDocWhs'),
    ('dbo', 'M_Agent'),
    ('dbo', 'M_Tugboat'),
    ('dbo', 'M_CARGO'),
    ('dbo', 'M_TBC'),
    ('dbo', 'release_notes'),
    ('dbo', 'M_DestinationPort'),
    ('dbo', 'M_Tongkang'),
    ('dbo', 'M_BP'),
    ('dbo', 'M_PortOfLoading'),
    ('dbo', 'doc_check_errors'),
    ('dbo', 'M_Jetty'),
    ('dbo', 'O_Roles'),
    ('dbo', 'T_SAP_KB'),
    ('dbo', 'doc_check_error_headers'),
    ('dbo', 'O_Permissions'),
    ('dbo', 'M_LocalItem'),
    ('dbo', 'M_MT'),
    ('dbo', 'L_ProcessDoc'),
    ('dbo', 'M_Classification'),
    ('dbo', 'master_surveyors'),
    ('dbo', 'L_MDOC'),
    ('dbo', 'master_tradings'),
    ('dbo', 'BEXP1'),
    ('dbo', 'M_User_tenant'),
    ('dbo', 'T_MDOC_Sign'),
    ('dbo', 'M_Category_item'),
    ('dbo', 'BIMP1'),
    ('dbo', 'M_Tenant_letter'),
    ('dbo', 'DocSignCoordinate'),
    ('dbo', 'MPS'),
    ('dbo', 'DocumentApproval'),
    ('dbo', 'T_MDOC_Header_inv_sub'),
    ('dbo', 'M_Remark'),
    ('dbo', 'MasterTempImport'),
    ('dbo', 'MPLPS'),
    ('dbo', 'T_MDOC_inv_sub'),
    ('dbo', 'M_Doc_attachment'),
    ('dbo', 'MPLTENANT'),
    ('dbo', 'form_cancels'),
    ('dbo', 'M_DisplayForm'),
    ('dbo', 'MPLSL'),
    ('dbo', 'form_cancel_items'),
    ('dbo', 'M_UserDisplayForm'),
    ('dbo', 'api_logs'),
    ('dbo', 'M_Country'),
    ('dbo', 'L_master'),
    ('dbo', 'M_Representative'),
    ('dbo', 'M_Trans_Type'),
    ('dbo', 'M_CatClassification'),
    ('dbo', 'ExportClassification'),
    ('dbo', 'WorkShifts'),
    ('dbo', 'BHLOCAL'),
    ('dbo', 'WorkShiftDetails'),
    ('dbo', 'sign_loggings'),
    ('dbo', 'revision_transactions'),
    ('dbo', 'T_MDOC1'),
    ('dbo', 'T_MDOC_Header'),
    ('dbo', 'T_MDOC_Header_bl'),
    ('dbo', 'R_Master'),
    ('dbo', 'T_MDOC_bl'),
    ('dbo', 'Signature'),
    ('dbo', 'R_Inv'),
    ('dbo', 'T_MDOC_Header_inv'),
    ('dbo', 'EmployeeWorkShifts'),
    ('dbo', 'notification_db'),
    ('dbo', 'T_MDOC_inv'),
    ('dbo', 'Departments'),
    ('dbo', 'notuls'),
    ('dbo', 'm_catalog_detail'),
    ('dbo', 'UserDepartment'),
    ('dbo', 'T_MDOC_Ref'),
    ('dbo', 'notul_headers'),
    ('dbo', 'T_MDOC_BZ'),
    ('dbo', 'posting_periods'),
    ('dbo', 'M_Tenant_Document_Config'),
    ('dbo', 'T_MDOC_Process'),
    ('dbo', 'T_MDOC_Logs'),
    ('dbo', 'DocQtyInv'),
    ('dbo', 'serial_numbers'),
    ('dbo', 'user_emails'),
    ('dbo', 'M_UserPpjk'),
    ('dbo', 'MasterHsCode'),
    ('dbo', 'Notifications'),
    ('dbo', 'activity_log'),
    ('dbo', 'hs_code_types'),
    ('dbo', 'M_Catalog'),
    ('dbo', 'FormStatement'),
    ('dbo', 'FormStatementHeader'),
    ('dbo', 'L_esign'),
    ('dbo', 'catalog_imgs'),
    ('dbo', 'MasterHsCodeName'),
    ('dbo', 'T_MDOC_Header_Local'),
    ('dbo', 'DocumentApprovalItem'),
    ('dbo', 'M_ExchangeRate'),
    ('dbo', 'AssisTug'),
    ('dbo', 'L_process_esign'),
    ('dbo', 'THEXP'),
    ('dbo', 'TEXP');


-- Variables used in loop
DECLARE 
    @SchemaName NVARCHAR(128),
    @TableName NVARCHAR(128),
    @OldPkName NVARCHAR(128),
    @FullColumnPath NVARCHAR(300),
    @RenameColumnSQL NVARCHAR(MAX),
    @DropConstraintSQL NVARCHAR(MAX),
    @AddConstraintSQL NVARCHAR(MAX);

DECLARE TableCursor CURSOR FOR
    SELECT SchemaName, TableName FROM @Tables;

OPEN TableCursor;
FETCH NEXT FROM TableCursor INTO @SchemaName, @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Processing table: ' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName);

    -- Step 2: Find existing PK constraint
    SELECT @OldPkName = kc.name
    FROM sys.key_constraints kc
    JOIN sys.tables t ON kc.parent_object_id = t.object_id
    JOIN sys.schemas s ON t.schema_id = s.schema_id
    WHERE kc.type = 'PK' AND t.name = @TableName AND s.name = @SchemaName;

    -- Step 3: Rename 'id' to 'DocEntry'
    SET @FullColumnPath = QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName) + '.id';
    SET @RenameColumnSQL = 'BEGIN TRY 
        EXEC sp_rename ''' + @FullColumnPath + ''', ''DocEntry'', ''COLUMN'';
    END TRY BEGIN CATCH 
        PRINT ''Rename failed for ' + @FullColumnPath + ': '' + ERROR_MESSAGE();
    END CATCH';
    EXEC sp_executesql @RenameColumnSQL;

    -- Step 4: Drop PK if exists
    IF @OldPkName IS NOT NULL
    BEGIN
        SET @DropConstraintSQL = 'ALTER TABLE ' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName) +
                                 ' DROP CONSTRAINT ' + QUOTENAME(@OldPkName) + ';';
        EXEC sp_executesql @DropConstraintSQL;
    END

    -- Step 5: Add new PK constraint
    SET @AddConstraintSQL = '
    BEGIN TRY
        ALTER TABLE ' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName) + '
        ADD CONSTRAINT PK_' + @TableName + '_DocEntry PRIMARY KEY (DocEntry);
    END TRY BEGIN CATCH 
        PRINT ''Add PK failed for ' + @TableName + ': '' + ERROR_MESSAGE();
    END CATCH';
    EXEC sp_executesql @AddConstraintSQL;

    -- Reset variable for next loop
    SET @OldPkName = NULL;

    FETCH NEXT FROM TableCursor INTO @SchemaName, @TableName;
END

CLOSE TableCursor;
DEALLOCATE TableCursor;


----------------------------------------------------------------------------------------------------
ALTER TABLE Notifications
ADD 
    Id UNIQUEIDENTIFIER NOT NULL CONSTRAINT DF_Notifications_Id DEFAULT NEWID(),
    CreationTime DATETIME2 NOT NULL CONSTRAINT DF_Notifications_CreationTime DEFAULT GETUTCDATE(),
    CreatorId UNIQUEIDENTIFIER NULL,
    LastModificationTime DATETIME2 NULL,
    LastModifierId UNIQUEIDENTIFIER NULL,
    IsDeleted BIT NOT NULL CONSTRAINT DF_Notifications_IsDeleted DEFAULT 0,
    DeleterId UNIQUEIDENTIFIER NULL,
    DeletionTime DATETIME2 NULL,
    ExtraProperties NVARCHAR(MAX) NOT NULL CONSTRAINT DF_Notifications_ExtraProperties DEFAULT '{}',
    ConcurrencyStamp NVARCHAR(40) NOT NULL CONSTRAINT DF_Notifications_ConcurrencyStamp DEFAULT NEWID();


-------------------------------------------------------------------------------------------------------

    -- Create unique index on Id
CREATE UNIQUE INDEX IX_Notifications_Id_Unique ON Notifications (Id);

-- Create index on IsDeleted
CREATE INDEX IX_Notifications_IsDeleted ON Notifications (IsDeleted);

-- Add check constraint for IsDeleted to ensure only 0 or 1
ALTER TABLE Notifications
ADD CONSTRAINT CK_Notifications_IsDeleted_0_1 CHECK (IsDeleted IN (0, 1));

-------------------------------------------------------------------------------------------------------


update THEXP
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where Deleted='Y'

update T_MDOC
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where Deleted='Y'

update T_MDOC_Header
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where Deleted='Y'

update T_MDOC_bl
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where Deleted='Y'


update T_MDOC_inv
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where Deleted='Y'


update T_MDOC_inv_sub
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where Deleted='Y'


update R_Master
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where deleted='Y'


update R_Inv
set IsDeleted=1,
DeletionTime='2025-06-22 14:43:17.8000000'
where deleted='Y'
