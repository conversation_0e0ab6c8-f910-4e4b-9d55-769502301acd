using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Master.Jetty.Dtos;

public class JettyCreateUpdateDto
{
    [Required]
    public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Alias { get; set; } = string.Empty;

    [Required]
    public decimal Max { get; set; }

    [Required]
    [StringLength(5)]
    public string Deleted { get; set; } = string.Empty;

    [Required]
    public int CreatedBy { get; set; }

    [Required]
    public int UpdatedBy { get; set; }

    [StringLength(255)]
    public string? Port { get; set; }
}