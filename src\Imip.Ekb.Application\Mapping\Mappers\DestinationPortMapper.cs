﻿using Imip.Ekb.Master.DestinationPort;
using Imip.Ekb.Master.DestinationPort.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class DestinationPortMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(DestinationPort.Id), nameof(DestinationPortDto.Id))]
    public partial DestinationPortDto MapToDto(DestinationPort entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(DestinationPort.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(DestinationPort.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(DestinationPort.CreatedAt))]
    public partial void MapToEntity(DestinationPortCreateUpdateDto dto, DestinationPort entity);

    // Custom mapping methods for complex scenarios
    public DestinationPort CreateEntityWithId(DestinationPortCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (DestinationPort)Activator.CreateInstance(typeof(DestinationPort), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<DestinationPortDto> MapToDtoList(List<DestinationPort> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<DestinationPortDto> MapToDtoEnumerable(IEnumerable<DestinationPort> entities);
}
