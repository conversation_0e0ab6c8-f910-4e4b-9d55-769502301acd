// Function to format permission labels
export function formatPermissionLabel(permission: string): string {
  if (!permission) return '';

  // Split by colon to get the type and name
  const parts = permission.split(':');
  if (parts.length !== 2) return permission;

  const [type, name] = parts;
  if (!name || !type) return permission;

  // Format based on type prefix
  switch (type) {
    case 'ept':
      return `${name.charAt(0).toUpperCase()}${name.slice(1)} Endpoint`;
    case 'gt':
      return `${name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Grant`;
    case 'rst':
      return `${name.charAt(0).toUpperCase()}${name.slice(1)} Response Type`;
    case 'scp':
      return `${name.charAt(0).toUpperCase()}${name.slice(1)} Scope`;
    default:
      return permission;
  }
}

// Function to generate a random client ID
export function generateClientId(): string {
  // List of adjectives and nouns to create app-like names
  const adjectives = ['swift', 'bright', 'clever', 'dynamic', 'efficient', 'flexible', 'global', 'innovative',
    'logical', 'modern', 'nimble', 'optimal', 'precise', 'quick', 'reliable', 'secure', 'stable',
    'tech', 'unified', 'virtual', 'wise', 'active', 'agile', 'bold', 'central', 'digital'];

  const nouns = ['app', 'api', 'system', 'platform', 'service', 'portal', 'hub', 'core', 'base',
    'cloud', 'data', 'engine', 'framework', 'grid', 'interface', 'logic', 'matrix', 'network',
    'object', 'project', 'resource', 'solution', 'tool', 'utility', 'vision', 'workspace'];

  // Pick random words
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];

  // Add a random number for uniqueness
  const uniqueNumber = Math.floor(Math.random() * 1000);

  // Combine to form app name
  return `${adjective}${noun}${uniqueNumber}`;
}

// Function to generate a random client secret
export function generateClientSecret(): string {
  // Generate a secret with only letters and numbers
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let secret = '';
  for (let i = 0; i < 32; i++) {
    secret += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return secret;
}

/**
 * Converts a comma-separated string into an array of trimmed strings
 * @param input - Comma-separated string, array of strings, or any input that can be converted to string
 * @returns Array of trimmed non-empty strings
 */
export function convertCommaSeparatedToArray(input: string | string[] | null | undefined): string[] {
  // If input is already an array, return it
  if (Array.isArray(input)) {
    return input.filter(item => item && item.length > 0);
  }

  // Otherwise, convert to string and split by comma
  return input
    ? String(input)
      .split(',')
      .map(item => item.trim())
      .filter(item => item.length > 0)
    : [];
}
