﻿using System;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone;
public interface IZoneDetailService
    : ICrudAppService<ZoneDetailDto, Guid, PagedAndSortedResultRequestDto, ZoneDetailCreateUpdateDto, ZoneDetailCreateUpdateDto>
{
    Task<PagedResultDto<ZoneDetailDto>> FilterListAsync(QueryParametersDto queryParametersDto);
}
