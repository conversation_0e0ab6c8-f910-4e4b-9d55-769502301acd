using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Jetty.Dtos;

public class JettyDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Alias { get; set; } = string.Empty;
    public decimal Max { get; set; }
    public string Deleted { get; set; } = string.Empty;
    public int CreatedBy { get; set; }
    public int UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Port { get; set; }
}