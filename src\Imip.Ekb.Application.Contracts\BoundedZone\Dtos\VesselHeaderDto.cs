using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.Dtos;

public class VesselHeaderDto : EntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public DateTime? VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string VesselType { get; set; } = string.Empty; // "Import", "Export", "LocalIn", "LocalOut"
    public List<VesselItemDto> Items { get; set; } = new();
    public CargoShortDto? Cargo { get; set; }
    public CargoShortDto? Barge { get; set; }
    public JettyShortDto? Jetty { get; set; }

    // Extra columns
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? AgentName { get; set; }
}

public class CargoShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Alias { get; set; }
    public string? Type { get; set; }
    public decimal? GrossWeight { get; set; }
}

public class JettyShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Alias { get; set; }
    public string? Port { get; set; }
    public decimal? Max { get; set; }
}