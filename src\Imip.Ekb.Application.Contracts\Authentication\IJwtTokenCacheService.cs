﻿using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Imip.Ekb.Authentication;
/// <summary>
/// Service interface for caching and processing JWT tokens to improve performance
/// </summary>
public interface IJwtTokenCacheService
{
    /// <summary>
    /// Gets cached token information or parses and caches the token
    /// </summary>
    /// <param name="token">The JWT token</param>
    /// <returns>Token information or null if invalid</returns>
    Task<JwtTokenInfo?> GetTokenInfoAsync(string token);

    /// <summary>
    /// Checks if a token is valid using cached information
    /// </summary>
    /// <param name="token">The JWT token</param>
    /// <returns>True if token is valid</returns>
    Task<bool> IsTokenValidAsync(string token);

    /// <summary>
    /// Gets a ClaimsPrincipal from cached token information
    /// </summary>
    /// <param name="token">The JWT token</param>
    /// <returns>ClaimsPrincipal or null if invalid</returns>
    Task<ClaimsPrincipal?> GetPrincipalAsync(string token);

    /// <summary>
    /// Invalidates a token from cache
    /// </summary>
    /// <param name="token">The JWT token to invalidate</param>
    Task InvalidateTokenAsync(string token);
}

/// <summary>
/// Serializable representation of a claim for caching purposes
/// </summary>
public class ClaimInfo
{
    /// <summary>
    /// The claim type
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// The claim value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// The claim value type (optional)
    /// </summary>
    public string ValueType { get; set; } = string.Empty;

    /// <summary>
    /// The claim issuer (optional)
    /// </summary>
    public string Issuer { get; set; } = string.Empty;
}

/// <summary>
/// Cached information about a JWT token
/// </summary>
public class JwtTokenInfo
{
    /// <summary>
    /// User ID from token claims
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Username from token claims
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Email from token claims
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Token expiration time
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Token not valid before time
    /// </summary>
    public DateTime NotBefore { get; set; }

    /// <summary>
    /// Token issued at time
    /// </summary>
    public DateTime IssuedAt { get; set; }

    /// <summary>
    /// All claims from the token (serializable format)
    /// </summary>
    public List<ClaimInfo> Claims { get; set; } = new();

    /// <summary>
    /// Whether the token is currently valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Hash of the token for caching purposes
    /// </summary>
    public string TokenHash { get; set; } = string.Empty;
}
