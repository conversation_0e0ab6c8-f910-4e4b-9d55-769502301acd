﻿using Imip.Ekb.Master.BcType;
using Imip.Ekb.Master.BcType.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class BcTypeMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(BcType.Id), nameof(BcTypeDto.Id))]
    [MapperIgnoreSource(nameof(BcType.IsDeleted))]
    [MapperIgnoreSource(nameof(BcType.DeleterId))]
    [MapperIgnoreSource(nameof(BcType.DeletionTime))]
    [MapperIgnoreSource(nameof(BcType.LastModificationTime))]
    [MapperIgnoreSource(nameof(BcType.LastModifierId))]
    [MapperIgnoreSource(nameof(BcType.CreationTime))]
    [MapperIgnoreSource(nameof(BcType.CreatorId))]
    [MapperIgnoreSource(nameof(BcType.ExtraProperties))]
    [MapperIgnoreSource(nameof(BcType.ConcurrencyStamp))]
    public partial BcTypeDto MapToDto(BcType entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(BcType.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(BcType.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(BcType.CreatedAt))]
    public partial void MapToEntity(BcTypeCreateUpdateDto dto, BcType entity);

    // Custom mapping methods for complex scenarios
    public BcType CreateEntityWithId(BcTypeCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (BcType)Activator.CreateInstance(typeof(BcType), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<BcTypeDto> MapToDtoList(List<BcType> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<BcTypeDto> MapToDtoEnumerable(IEnumerable<BcType> entities);
}
