﻿using Imip.Ekb.Master.PortService;
using Imip.Ekb.Master.PortService.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class PortServiceMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    public partial PortServiceDto MapToDto(PortService entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(PortService.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(PortService.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(PortService.CreatedAt))]
    public partial void MapToEntity(PortServiceCreateUpdateDto dto, PortService entity);

    // Custom mapping methods for complex scenarios
    public PortService CreateEntityWithId(PortServiceCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (PortService)Activator.CreateInstance(typeof(PortService), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<PortServiceDto> MapToDtoList(List<PortService> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<PortServiceDto> MapToDtoEnumerable(IEnumerable<PortService> entities);
}
