# Mapperly with Complex Nested Relationships in ABP Framework

This guide demonstrates how to handle complex nested relationships when using Mapperly in ABP Framework Application Services.

## Scenarios Covered

1. **Simple Nested Properties** (Automatic)
2. **Complex Collections** (Custom Handling)
3. **Circular References** (Prevention)
4. **Performance-Optimized Projections**
5. **Conditional Nested Loading**

## Simple Nested Properties (Works Automatically)

### Example: Order with Customer and Address

```csharp
// Entities
public class Order : AuditedAggregateRoot<Guid>
{
    public string OrderNumber { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public decimal TotalAmount { get; set; }
    
    // Simple navigation properties - Mapperly handles these automatically
    public Guid CustomerId { get; set; }
    public Customer Customer { get; set; } = null!;
    public Address ShippingAddress { get; set; } = null!;
}

public class Customer : AuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}

public class Address : ValueObject
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Street;
        yield return City;
        yield return Country;
    }
}

// DTOs
public class OrderDto : AuditedEntityDto<Guid>
{
    public string OrderNumber { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public decimal TotalAmount { get; set; }
    public Guid CustomerId { get; set; }
    public CustomerDto Customer { get; set; } = null!;
    public AddressDto ShippingAddress { get; set; } = null!;
}

public class CustomerDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}

public class AddressDto
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
}

// Mapper - Handles nested properties automatically
[Mapper]
public partial class OrderMapper : IAbpMapper<Order, OrderDto, Guid, CreateUpdateOrderDto>
{
    [MapperIgnoreSource(nameof(Order.ExtraProperties))]
    [MapperIgnoreSource(nameof(Order.ConcurrencyStamp))]
    public partial OrderDto ToDto(Order entity);

    public partial List<OrderDto> ToDto(List<Order> entities);

    public Order ToEntity(CreateUpdateOrderDto createDto)
    {
        return new Order(
            Guid.NewGuid(),
            createDto.OrderNumber,
            createDto.OrderDate,
            createDto.TotalAmount,
            createDto.CustomerId,
            createDto.ShippingAddress
        );
    }

    public void UpdateEntity(CreateUpdateOrderDto updateDto, Order entity)
    {
        entity.OrderNumber = updateDto.OrderNumber;
        entity.OrderDate = updateDto.OrderDate;
        entity.TotalAmount = updateDto.TotalAmount;
        entity.CustomerId = updateDto.CustomerId;
        entity.ShippingAddress = updateDto.ShippingAddress;
    }
}

// Application Service - Uses standard base class
public class OrderAppService : MapperlyCrudAppService<Order, OrderDto, Guid, GetOrderListDto, CreateUpdateOrderDto, OrderMapper>, IOrderAppService
{
    public OrderAppService(IRepository<Order, Guid> repository, OrderMapper mapper) : base(repository, mapper)
    {
    }
}
```

## Complex Nested Collections (Custom Handling Required)

### Example: Customer with Orders and Order Items

```csharp
// Entities with complex relationships
public class Customer : AuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    
    // Complex collection - needs custom handling
    public List<Order> Orders { get; set; } = new();
}

public class Order : AuditedAggregateRoot<Guid>
{
    public string OrderNumber { get; set; } = string.Empty;
    public Guid CustomerId { get; set; }
    public Customer Customer { get; set; } = null!;
    
    // Nested collection
    public List<OrderItem> OrderItems { get; set; } = new();
}

public class OrderItem : Entity<Guid>
{
    public Guid OrderId { get; set; }
    public Order Order { get; set; } = null!;
    public Guid ProductId { get; set; }
    public Product Product { get; set; } = null!;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
}

// DTOs
public class CustomerDetailDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public List<OrderSummaryDto> Orders { get; set; } = new();
}

public class OrderSummaryDto : AuditedEntityDto<Guid>
{
    public string OrderNumber { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public int ItemCount { get; set; }
}

// Enhanced Mapper with custom nested handling
[Mapper]
public partial class CustomerMapper : IAbpMapper<Customer, CustomerDto, Guid, CreateUpdateCustomerDto>
{
    [MapperIgnoreSource(nameof(Customer.ExtraProperties))]
    [MapperIgnoreSource(nameof(Customer.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(Customer.Orders))] // Ignore complex collection
    public partial CustomerDto ToDto(Customer entity);

    public partial List<CustomerDto> ToDto(List<Customer> entities);

    // Custom method for detailed mapping with nested collections
    public CustomerDetailDto ToDetailDto(Customer entity)
    {
        var dto = new CustomerDetailDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Email = entity.Email,
            CreationTime = entity.CreationTime,
            CreatorId = entity.CreatorId,
            LastModificationTime = entity.LastModificationTime,
            LastModifierId = entity.LastModifierId,
            
            // Custom mapping for complex nested collection
            Orders = entity.Orders?.Select(order => new OrderSummaryDto
            {
                Id = order.Id,
                OrderNumber = order.OrderNumber,
                TotalAmount = order.OrderItems?.Sum(item => item.Quantity * item.UnitPrice) ?? 0,
                ItemCount = order.OrderItems?.Count ?? 0,
                CreationTime = order.CreationTime
            }).ToList() ?? new List<OrderSummaryDto>()
        };

        return dto;
    }

    // Standard entity creation
    public Customer ToEntity(CreateUpdateCustomerDto createDto)
    {
        return new Customer(Guid.NewGuid(), createDto.Name, createDto.Email);
    }

    public void UpdateEntity(CreateUpdateCustomerDto updateDto, Customer entity)
    {
        entity.Name = updateDto.Name;
        entity.Email = updateDto.Email;
    }
}

// Enhanced Application Service with nested relationship handling
public class CustomerAppService : EnhancedMapperlyCrudAppService<Customer, CustomerDto, Guid, GetCustomerListDto, CreateUpdateCustomerDto, CustomerMapper>, ICustomerAppService
{
    private readonly IRepository<Order, Guid> _orderRepository;

    public CustomerAppService(
        IRepository<Customer, Guid> repository,
        CustomerMapper mapper,
        IRepository<Order, Guid> orderRepository) : base(repository, mapper)
    {
        _orderRepository = orderRepository;
    }

    // Override to provide custom nested mapping
    protected override CustomerDto MapToEntityDtoWithIncludes(Customer entity, bool includeNestedEntities = true, int maxDepth = 3)
    {
        if (includeNestedEntities && maxDepth > 0)
        {
            return GetMapper().ToDetailDto(entity);
        }
        
        return GetMapper().ToDto(entity);
    }

    // Override to handle nested entities during creation
    protected override async Task HandleNestedEntitiesOnCreateAsync(Customer entity, CreateUpdateCustomerDto input)
    {
        // Handle any nested entity creation logic here
        // For example, creating default orders, preferences, etc.
        await Task.CompletedTask;
    }

    // Custom method to get customer with full order details
    public async Task<CustomerDetailDto> GetCustomerWithOrdersAsync(Guid id)
    {
        var customer = await Repository.GetAsync(id, includeDetails: true);
        return GetMapper().ToDetailDto(customer);
    }

    // Performance-optimized list without nested data
    protected override List<CustomerDto> MapToEntityDtosWithProjection(List<Customer> entities, bool includeNestedEntities = false)
    {
        // For list views, never include nested orders for performance
        return GetMapper().ToDto(entities);
    }
}
```

## Circular Reference Prevention

### Example: Category Hierarchy

```csharp
// Entity with potential circular references
public class Category : AuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = string.Empty;
    public Guid? ParentCategoryId { get; set; }
    public Category? ParentCategory { get; set; }
    public List<Category> SubCategories { get; set; } = new();
}

// DTO
public class CategoryDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public Guid? ParentCategoryId { get; set; }
    public CategoryDto? ParentCategory { get; set; }
    public List<CategoryDto> SubCategories { get; set; } = new();
}

// Mapper with circular reference handling
[Mapper]
public partial class CategoryMapper : IAbpMapper<Category, CategoryDto, Guid, CreateUpdateCategoryDto>
{
    [MapperIgnoreSource(nameof(Category.ExtraProperties))]
    [MapperIgnoreSource(nameof(Category.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(Category.ParentCategory))] // Ignore to prevent circular reference
    [MapperIgnoreSource(nameof(Category.SubCategories))] // Ignore to prevent circular reference
    public partial CategoryDto ToDto(Category entity);

    // Custom method with circular reference prevention
    public CategoryDto ToDtoWithHierarchy(Category entity, int maxDepth = 3)
    {
        return ToDtoWithHierarchy(entity, new HashSet<Guid>(), 0, maxDepth);
    }

    private CategoryDto ToDtoWithHierarchy(Category entity, HashSet<Guid> visitedIds, int currentDepth, int maxDepth)
    {
        if (currentDepth >= maxDepth || visitedIds.Contains(entity.Id))
        {
            // Return basic DTO without nested data to prevent circular reference
            return ToDto(entity);
        }

        visitedIds.Add(entity.Id);

        var dto = new CategoryDto
        {
            Id = entity.Id,
            Name = entity.Name,
            ParentCategoryId = entity.ParentCategoryId,
            CreationTime = entity.CreationTime,
            
            // Safely map parent (only if not already visited)
            ParentCategory = entity.ParentCategory != null && !visitedIds.Contains(entity.ParentCategory.Id)
                ? ToDtoWithHierarchy(entity.ParentCategory, visitedIds, currentDepth + 1, maxDepth)
                : null,
                
            // Safely map subcategories
            SubCategories = entity.SubCategories?
                .Where(sub => !visitedIds.Contains(sub.Id))
                .Select(sub => ToDtoWithHierarchy(sub, visitedIds, currentDepth + 1, maxDepth))
                .ToList() ?? new List<CategoryDto>()
        };

        visitedIds.Remove(entity.Id);
        return dto;
    }
}

// Application Service with circular reference handling
public class CategoryAppService : EnhancedMapperlyCrudAppService<Category, CategoryDto, Guid, GetCategoryListDto, CreateUpdateCategoryDto, CategoryMapper>, ICategoryAppService
{
    public CategoryAppService(IRepository<Category, Guid> repository, CategoryMapper mapper) : base(repository, mapper)
    {
    }

    protected override CategoryDto MapToEntityDtoWithIncludes(Category entity, bool includeNestedEntities = true, int maxDepth = 3)
    {
        if (includeNestedEntities)
        {
            return GetMapper().ToDtoWithHierarchy(entity, maxDepth);
        }
        
        return GetMapper().ToDto(entity);
    }

    // Custom method to get category tree
    public async Task<List<CategoryDto>> GetCategoryTreeAsync()
    {
        var rootCategories = await Repository.GetListAsync(c => c.ParentCategoryId == null);
        return rootCategories.Select(c => GetMapper().ToDtoWithHierarchy(c, maxDepth: 5)).ToList();
    }
}
```

## Performance Considerations

### 1. Use Projections for List Views

```csharp
// Create lightweight DTOs for list views
public class CustomerListItemDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int OrderCount { get; set; }
}

// Mapper method for optimized list projection
public CustomerListItemDto ToListItemDto(Customer entity)
{
    return new CustomerListItemDto
    {
        Id = entity.Id,
        Name = entity.Name,
        Email = entity.Email,
        OrderCount = entity.Orders?.Count ?? 0
    };
}
```

### 2. Conditional Loading Based on Context

```csharp
protected override CustomerDto MapToEntityDtoWithContext(Customer entity, string context = "default")
{
    return context switch
    {
        "detail" => GetMapper().ToDetailDto(entity), // Full nested data
        "list" => GetMapper().ToDto(entity), // Basic data only
        "summary" => GetMapper().ToListItemDto(entity), // Minimal data
        _ => GetMapper().ToDto(entity)
    };
}
```

## Summary

### ✅ **What Works Automatically:**
- Simple navigation properties
- Value objects
- Basic collections with matching property names

### 🔧 **What Needs Custom Handling:**
- Complex nested collections with business logic
- Circular references
- Performance-optimized projections
- Conditional nested loading
- Deep hierarchies

### 🚀 **Best Practices:**
1. Use `EnhancedMapperlyCrudAppService<>` for complex scenarios
2. Override mapping methods for custom nested handling
3. Implement circular reference prevention for hierarchical data
4. Use context-based mapping for different scenarios
5. Create lightweight DTOs for list views
6. Handle nested entities in separate methods

The enhanced base class provides the foundation, but complex nested relationships require thoughtful custom implementation in your mappers and application services.

## EF Core Include Support with Mapperly

### Enhanced Base Class with EF Core Includes

The `EfCoreMapperlyCrudAppService<>` provides built-in support for EF Core's `.Include()` and `.ThenInclude()` methods:

```csharp
// Example: Order Application Service with EF Core Includes
public class OrderAppService : EfCoreMapperlyCrudAppService<Order, OrderDto, Guid, GetOrderListDto, CreateUpdateOrderDto, OrderMapper>, IOrderAppService
{
    public OrderAppService(IRepository<Order, Guid> repository, OrderMapper mapper) : base(repository, mapper)
    {
        GetPolicyName = EkbPermissions.Orders.Default;
        GetListPolicyName = EkbPermissions.Orders.Default;
        CreatePolicyName = EkbPermissions.Orders.Create;
        UpdatePolicyName = EkbPermissions.Orders.Edit;
        DeletePolicyName = EkbPermissions.Orders.Delete;
    }

    // Configure includes for detail views (single entity operations)
    protected override IQueryable<Order> ApplyDetailIncludes(IQueryable<Order> query)
    {
        return query
            .Include(o => o.Customer)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
            .Include(o => o.ShippingAddress);
    }

    // Configure minimal includes for list views (performance optimized)
    protected override IQueryable<Order> ApplyListIncludes(IQueryable<Order> query)
    {
        return query
            .Include(o => o.Customer); // Only include customer for list view
    }

    // Configure includes for edit operations
    protected override IQueryable<Order> ApplyEditIncludes(IQueryable<Order> query)
    {
        return query
            .Include(o => o.Customer)
            .Include(o => o.OrderItems); // Include items for editing, but not products
    }
}
```

### Complex Nested Relationships Example

```csharp
// Customer with Orders and Order Items
public class CustomerAppService : EfCoreMapperlyCrudAppService<Customer, CustomerDto, Guid, GetCustomerListDto, CreateUpdateCustomerDto, CustomerMapper>, ICustomerAppService
{
    public CustomerAppService(IRepository<Customer, Guid> repository, CustomerMapper mapper) : base(repository, mapper)
    {
    }

    // Detail view: Load customer with all orders and their items
    protected override IQueryable<Customer> ApplyDetailIncludes(IQueryable<Customer> query)
    {
        return query
            .Include(c => c.Orders)
                .ThenInclude(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
            .Include(c => c.Orders)
                .ThenInclude(o => o.ShippingAddress)
            .Include(c => c.DefaultAddress);
    }

    // List view: No nested data for performance
    protected override IQueryable<Customer> ApplyListIncludes(IQueryable<Customer> query)
    {
        // No includes for list view to avoid N+1 problems
        return query;
    }

    // Edit view: Load customer with orders but not order items
    protected override IQueryable<Customer> ApplyEditIncludes(IQueryable<Customer> query)
    {
        return query
            .Include(c => c.Orders)
            .Include(c => c.DefaultAddress);
    }

    // Custom method: Get customer with recent orders only
    public async Task<CustomerDto> GetCustomerWithRecentOrdersAsync(Guid id)
    {
        var query = await EfRepository.GetQueryableAsync();

        var customer = await AsyncExecuter.FirstOrDefaultAsync(
            query
                .Include(c => c.Orders.Where(o => o.OrderDate >= DateTime.Now.AddMonths(-3)))
                    .ThenInclude(o => o.OrderItems)
                .Where(c => c.Id == id));

        if (customer == null)
        {
            throw new EntityNotFoundException(typeof(Customer), id);
        }

        return MapToGetOutputDto(customer);
    }
}
```

### Hierarchical Data with Circular Reference Prevention

```csharp
// Category hierarchy with safe includes
public class CategoryAppService : EfCoreMapperlyCrudAppService<Category, CategoryDto, Guid, GetCategoryListDto, CreateUpdateCategoryDto, CategoryMapper>, ICategoryAppService
{
    public CategoryAppService(IRepository<Category, Guid> repository, CategoryMapper mapper) : base(repository, mapper)
    {
    }

    // Detail view: Load category with parent and immediate children only
    protected override IQueryable<Category> ApplyDetailIncludes(IQueryable<Category> query)
    {
        return query
            .Include(c => c.ParentCategory)
            .Include(c => c.SubCategories); // Only immediate children to avoid deep recursion
    }

    // List view: Load with parent for breadcrumb display
    protected override IQueryable<Category> ApplyListIncludes(IQueryable<Category> query)
    {
        return query
            .Include(c => c.ParentCategory);
    }

    // Custom method: Get full category tree (root categories with all descendants)
    public async Task<List<CategoryDto>> GetCategoryTreeAsync()
    {
        var query = await EfRepository.GetQueryableAsync();

        // Load root categories with all descendants (be careful with depth)
        var rootCategories = await AsyncExecuter.ToListAsync(
            query
                .Include(c => c.SubCategories)
                    .ThenInclude(sc => sc.SubCategories)
                        .ThenInclude(ssc => ssc.SubCategories) // Max 3 levels deep
                .Where(c => c.ParentCategoryId == null));

        return MapEntitiesToDtos(rootCategories);
    }
}
```

### Performance Optimization Strategies

```csharp
// Product with optimized includes based on context
public class ProductAppService : EfCoreMapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>, IProductAppService
{
    public ProductAppService(IRepository<Product, Guid> repository, ProductMapper mapper) : base(repository, mapper)
    {
    }

    // Detail view: Full product information
    protected override IQueryable<Product> ApplyDetailIncludes(IQueryable<Product> query)
    {
        return query
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.ProductAttributes)
                .ThenInclude(pa => pa.AttributeValue)
            .Include(p => p.Reviews.Take(10)) // Limit reviews to prevent large datasets
                .ThenInclude(r => r.Customer);
    }

    // List view: Minimal data for grid display
    protected override IQueryable<Product> ApplyListIncludes(IQueryable<Product> query)
    {
        return query
            .Include(p => p.Category) // Only category for filtering/display
            .Include(p => p.ProductImages.Take(1)); // Only first image for thumbnail
    }

    // Search results: Optimized for search scenarios
    public async Task<PagedResultDto<ProductDto>> SearchProductsAsync(ProductSearchInput input)
    {
        var query = await CreateFilteredQueryAsync(input);

        // Apply search-specific includes
        query = query
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1));

        var totalCount = await AsyncExecuter.CountAsync(query);
        query = ApplySorting(query, input);
        query = ApplyPaging(query, input);

        var entities = await AsyncExecuter.ToListAsync(query);
        var dtos = MapEntitiesToDtos(entities);

        return new PagedResultDto<ProductDto>(totalCount, dtos);
    }

    // Override to add performance monitoring
    protected override void LogIncludePerformance(string operation, int entityCount, TimeSpan duration)
    {
        base.LogIncludePerformance(operation, entityCount, duration);

        // Custom performance logging
        if (entityCount > 100 && duration.TotalMilliseconds > 500)
        {
            Logger.LogWarning(
                "Large dataset query in {Operation}: Consider pagination or reducing includes. " +
                "Entities: {EntityCount}, Duration: {Duration}ms",
                operation, entityCount, duration.TotalMilliseconds);
        }
    }
}
```

### Best Practices for EF Core Includes with Mapperly

1. **Context-Specific Includes**: Use different include strategies for different operations
2. **Avoid N+1 Problems**: Always include related data that will be accessed
3. **Limit Deep Includes**: Prevent performance issues with deep object graphs
4. **Monitor Performance**: Log slow queries and large datasets
5. **Use Projections**: For read-only scenarios, consider using projections instead of full entities

```csharp
// Example: Projection for read-only list views
public async Task<PagedResultDto<ProductListDto>> GetProductListOptimizedAsync(GetProductListDto input)
{
    var query = await EfRepository.GetQueryableAsync();

    // Use projection instead of includes for better performance
    var projectionQuery = query
        .Select(p => new ProductListDto
        {
            Id = p.Id,
            Name = p.Name,
            Price = p.Price,
            CategoryName = p.Category.Name,
            ImageUrl = p.ProductImages.FirstOrDefault().Url,
            IsActive = p.IsActive
        });

    var totalCount = await AsyncExecuter.CountAsync(projectionQuery);
    projectionQuery = projectionQuery.Skip(input.SkipCount).Take(input.MaxResultCount);

    var dtos = await AsyncExecuter.ToListAsync(projectionQuery);
    return new PagedResultDto<ProductListDto>(totalCount, dtos);
}
```
