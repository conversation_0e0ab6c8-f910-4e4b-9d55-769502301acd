using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Imip.Ekb.Web.Services;

namespace Imip.Ekb.Web.Middleware;

/// <summary>
/// Middleware that intercepts requests to /api/abp/application-configuration
/// and proxies them to the identity server using AppToAppService
/// </summary>
public class ApplicationConfigurationProxyMiddleware : IMiddleware, ITransientDependency
{
    private readonly AppToAppService _appToAppService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ApplicationConfigurationProxyMiddleware> _logger;

    public ApplicationConfigurationProxyMiddleware(
        AppToAppService appToAppService,
        IConfiguration configuration,
        ILogger<ApplicationConfigurationProxyMiddleware> logger)
    {
        _appToAppService = appToAppService;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Check if this is a request to the application configuration endpoint
        if (context.Request.Path.StartsWithSegments("/api/abp/application-configuration"))
        {
            _logger.LogDebug("Intercepting application configuration request: {Path}", context.Request.Path);

            // Check if this is a Bearer token request (external API) or cookie request (internal frontend)
            var authHeader = context.Request.Headers.Authorization.FirstOrDefault();
            var hasBearerToken = !string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase);

            if (hasBearerToken)
            {
                // External API request with Bearer token - proxy to identity server
                _logger.LogDebug("Bearer token detected, proxying to identity server");
                await HandleApplicationConfigurationRequestAsync(context);
                return;
            }
            else
            {
                // Internal frontend request with cookies - use local ABP controller
                _logger.LogDebug("Cookie-based request detected, using local ABP controller");
                // Let the request continue to the local ABP controller
                await next(context);
                return;
            }
        }

        // Continue with the next middleware
        await next(context);
    }

    private async Task HandleApplicationConfigurationRequestAsync(HttpContext context)
    {
        try
        {
            var identityServerUrl = _configuration["AuthServer:Authority"];
            if (string.IsNullOrEmpty(identityServerUrl))
            {
                _logger.LogError("AuthServer:Authority not configured");
                context.Response.StatusCode = 500;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync("{\"error\":\"Identity server not configured\"}");
                return;
            }

            // Build the target URL with query parameters
            var queryString = context.Request.QueryString.ToString();
            var endpoint = $"/api/abp/application-configuration{queryString}";

            _logger.LogDebug("Proxying application configuration request to: {IdentityServer}{Endpoint}", identityServerUrl, endpoint);

            // Use AppToAppService to make the call with proper token handling
            var result = await _appToAppService.CallOtherAppAsync<object>(identityServerUrl, endpoint);

            // Return the result as JSON
            context.Response.StatusCode = 200;
            context.Response.ContentType = "application/json";

            var jsonResponse = JsonSerializer.Serialize(result, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            await context.Response.WriteAsync(jsonResponse);

            _logger.LogDebug("Successfully proxied application configuration from identity server");
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access while requesting application configuration from identity server");
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync("{\"error\":\"Unauthorized access\",\"details\":\"No valid access token available\"}");
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while requesting application configuration from identity server");
            context.Response.StatusCode = 502;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync($"{{\"error\":\"Failed to connect to identity server\",\"details\":\"{ex.Message}\"}}");
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while requesting application configuration from identity server");
            context.Response.StatusCode = 504;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync("{\"error\":\"Timeout connecting to identity server\"}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while requesting application configuration from identity server");
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync($"{{\"error\":\"Internal server error\",\"details\":\"{ex.Message}\"}}");
        }
    }
}

/// <summary>
/// Extension methods for ApplicationConfigurationProxyMiddleware
/// </summary>
public static class ApplicationConfigurationProxyMiddlewareExtensions
{
    /// <summary>
    /// Adds the application configuration proxy middleware to the pipeline
    /// </summary>
    /// <param name="builder">The application builder</param>
    /// <returns>The application builder</returns>
    public static IApplicationBuilder UseApplicationConfigurationProxy(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ApplicationConfigurationProxyMiddleware>();
    }
}
