﻿using Imip.Ekb.Master.Tenant;
using Imip.Ekb.Master.Tenant.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class TenantMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Tenant.Id), nameof(MasterTenantDto.Id))]
    public partial MasterTenantDto MapToDto(Tenant entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(Tenant.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(Tenant.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(Tenant.CreatedAt))]
    public partial void MapToEntity(TenantCreateUpdateDto dto, Tenant entity);

    // Custom mapping methods for complex scenarios
    public Tenant CreateEntityWithId(TenantCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Tenant)Activator.CreateInstance(typeof(Tenant), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<MasterTenantDto> MapToDtoList(List<Tenant> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<MasterTenantDto> MapToDtoEnumerable(IEnumerable<Tenant> entities);
}
