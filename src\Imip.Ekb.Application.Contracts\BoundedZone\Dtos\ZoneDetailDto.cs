using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.Dtos;

public class ZoneDetailDto : EntityDto<Guid>
{
    public int DocEntry { get; set; }
    public int? BcTypeKey { get; set; }
    public int? TenantKey { get; set; }
    public string? Bp { get; set; }
    public string? Cargo { get; set; }
    public decimal? Weight { get; set; }
    public string? BlNo { get; set; }
    public DateOnly? BlDate { get; set; }
    public string? AjuNo { get; set; }
    public string? RegNo { get; set; }
    public DateOnly? RegDate { get; set; }
    public string? SppbNo { get; set; }
    public DateOnly? SppbDate { get; set; }
    public string? SppdNo { get; set; }
    public DateOnly? SppdDate { get; set; }
    public string? Shipment { get; set; }
    public string? Remarks { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int? CreatedId { get; set; }
    public string? UpdatedBy { get; set; }
    public int? UpdatedId { get; set; }
    public string? Color { get; set; }
    public int? SapKbEntry { get; set; }
    public string? NoBl { get; set; }
    public DateOnly? DateBl { get; set; }
    public string? NoInv { get; set; }
    public DateOnly? DateInv { get; set; }
    public string? ShipmentNo { get; set; }
    public DateOnly? EbillingDate { get; set; }
    public string? Skep { get; set; }
    public DateOnly? SkepDate { get; set; }
    public string? PibNo { get; set; }
    public DateOnly? PibDate { get; set; }
    public DateOnly? VesselArrive { get; set; }
    public DateOnly? ExpiredDate { get; set; }
    public string? Item { get; set; }
    public decimal? Qty { get; set; }
    public decimal? Amount { get; set; }
    public string? Status { get; set; }
    public string? SiteStatus { get; set; }
    public int? DocNum { get; set; }
    public string? Flags { get; set; }
    public string? OceanFreight { get; set; }
    public string? Currency { get; set; }
    public string? Ocean { get; set; }
    public string? Cbmb { get; set; }
    public decimal? FreightValue { get; set; }
    public string? Attachment { get; set; }
    public DateOnly? PostDate { get; set; }
    public string DocType { get; set; } = string.Empty;
    public string IsScan { get; set; } = string.Empty;
    public string IsOriginal { get; set; } = string.Empty;
    public string IsSend { get; set; } = string.Empty;
    public string IsFeOri { get; set; } = string.Empty;
    public string IsFeSend { get; set; } = string.Empty;
    public string? SecretKey { get; set; }
    public int? Ppjk { get; set; }
    public string? PpjkcodeTemp { get; set; }
    public string? PortOfLoading { get; set; }
    public DateOnly? EmailToPpjk { get; set; }
    public string? LetterNo { get; set; }
    public string? ItemName { get; set; }
    public decimal? ItemQty { get; set; }
    public string? UnitQty { get; set; }
    public decimal? GrossWeight { get; set; }
    public string? UnitWeight { get; set; }
    public string? MatchKey { get; set; }
    public int? Bpnum { get; set; }
    public int? CargoNum { get; set; }
    public int? LineNum { get; set; }
    public string IsChange { get; set; } = string.Empty;
    public string Deleted { get; set; } = string.Empty;
    public DateOnly? SppbUpdateDate { get; set; }
    public string? SppbNoUpdate { get; set; }
    public DateTime? SppbDateUpdate { get; set; }
    public string? SppdNoUpdate { get; set; }
    public DateTime? SppdDateUpdate { get; set; }
    public DateTime? DeletedAt { get; set; }
    public string? DeleteBy { get; set; }
    public string? EBillingNo { get; set; }
    public string? ContractNo { get; set; }
    public string? OpenDate { get; set; }
    public string? UpdateDate { get; set; }
    public string? InternalCode { get; set; }
    public DateOnly? ContractDate { get; set; }
    public string? RegType { get; set; }
    public decimal? Cbm { get; set; }
    public string? Notification { get; set; }
    public string? Sppbstatus { get; set; }
    public int? Agent { get; set; }
    public int? BillingId { get; set; }
    public string? InsuranceCurrency { get; set; }
    public decimal? InsuranceValue { get; set; }
    public int? DestinationPortId { get; set; }
    public decimal? NetWeight { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? TotalInv { get; set; }
    public decimal? QtyEstimate { get; set; }
    public decimal? PriceEstimate { get; set; }
    public string? BillingType { get; set; }
    public string? ChargeTo { get; set; }
    public decimal? QtyRevised { get; set; }
    public decimal? PriceRevised { get; set; }
    public string? NoNota { get; set; }
    public decimal? TotalEstimate { get; set; }
    public decimal? TotalRevised { get; set; }
    public string? SerialNumber { get; set; }
    public string? SerialNumber1 { get; set; }
    public string? SerialNumber2 { get; set; }
    public string? SerialNumber3 { get; set; }
    public string? SerialNumber4 { get; set; }
    public string? SerialNumber5 { get; set; }
    public string? SerialNumber6 { get; set; }
    public string? IsParent { get; set; }
    public decimal? GrtVessel { get; set; }
    public string? NpwpBp { get; set; }
    public int? EsignDecimal { get; set; }
    public long? CargoId { get; set; }
    public long? BargeId { get; set; }
    public string? Voyage { get; set; }
    public string? VesselName { get; set; }
    public string? ProcessName { get; set; }
    public decimal? Rate { get; set; }
    public decimal? Bm { get; set; }
    public decimal? Ppn { get; set; }
    public decimal? Pph { get; set; }
    public decimal? Bmad { get; set; }
    public decimal? Bmtp { get; set; }
    public string? FormType { get; set; }
    public DateOnly? BillingDate { get; set; }
    public string IsUrgent { get; set; } = string.Empty;
    public long? SurveyorId { get; set; }
    public string? SurveyorName { get; set; }
    public DateOnly? EmailToBcDate { get; set; }
    public decimal? Container { get; set; }
    public string? ExportClassificationId { get; set; }
    public long? WarehouseId { get; set; }
    public decimal? IncreaseValue { get; set; }
    public decimal? DecreaseValue { get; set; }
    public decimal? IncreaseValuePpn { get; set; }
    public decimal? DecreaseValuePpn { get; set; }
    public decimal? IncreaseValuePph { get; set; }
    public decimal? DecreaseValuePph { get; set; }
    public string? RepairLocation { get; set; }
    public long? RepresentativeId { get; set; }
    public long? InvoiceDetailId { get; set; }
    public decimal? CostOfRepair { get; set; }
    public string? ItemCategoryCode { get; set; }
    public string? ItemCategoryDescription { get; set; }
    public string? SapBillingStatus { get; set; }
}