# Architectural Solutions for Eliminating Mapperly Code Duplication in ABP Framework

This document presents multiple architectural approaches to eliminate code duplication when using Mapperly in ABP Framework Application Services.

## Problem Statement

When using Mapperly instead of AutoMapper in ABP Framework, each Application Service needs to override the same mapping methods:

```csharp
// This code needs to be duplicated in every Application Service
protected override TEntityDto MapToGetOutputDto(TEntity entity)
{
    return _mapper.ToDto(entity);
}

protected override TEntityDto MapToGetListOutputDto(TEntity entity)
{
    return _mapper.ToDto(entity);
}

protected override TEntity MapToEntity(TCreateInput createInput)
{
    return _mapper.ToEntity(createInput);
}

protected override void MapToEntity(TUpdateInput updateInput, TEntity entity)
{
    _mapper.UpdateEntity(updateInput, entity);
}
```

## Solution 1: Generic Base Application Service (Recommended)

### Implementation

Create a generic base class that handles all Mapperly mapping operations:

```csharp
// src/YourProject.Application/Services/MapperlyCrudAppService.cs
public abstract class MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper> :
    CrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateInput : class
    where TUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
{
    protected readonly TMapper Mapper;

    protected MapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository)
    {
        Mapper = mapper;
    }

    #region Mapperly Override Methods

    protected override TEntityDto MapToGetOutputDto(TEntity entity)
    {
        return Mapper.ToDto(entity);
    }

    protected override TEntityDto MapToGetListOutputDto(TEntity entity)
    {
        return Mapper.ToDto(entity);
    }

    protected override TEntity MapToEntity(TCreateInput createInput)
    {
        return Mapper.ToEntity(createInput);
    }

    protected override void MapToEntity(TUpdateInput updateInput, TEntity entity)
    {
        Mapper.UpdateEntity(updateInput, entity);
    }

    protected override Task<List<TEntityDto>> MapToGetListOutputDtosAsync(List<TEntity> entities)
    {
        return Task.FromResult(Mapper.ToDto(entities));
    }

    #endregion

    #region Helper Methods for Derived Classes

    protected TMapper GetMapper() => Mapper;
    protected TEntityDto MapEntityToDto(TEntity entity) => Mapper.ToDto(entity);
    protected List<TEntityDto> MapEntitiesToDtos(List<TEntity> entities) => Mapper.ToDto(entities);

    #endregion
}

// Simplified version for cases where Create and Update DTOs are the same
public abstract class MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TMapper> :
    MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TCreateUpdateInput, TMapper>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateUpdateInput, TCreateUpdateInput>
{
    protected MapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository, mapper)
    {
    }
}
```

### Usage

Application Services become much simpler:

```csharp
[Authorize(YourProjectPermissions.Products.Default)]
public class ProductAppService :
    MapperlyCrudAppService<
        Product,                     // The entity
        ProductDto,                  // Used to show products
        Guid,                        // Primary key type
        GetProductListDto,           // Used for paging/sorting/filtering
        CreateUpdateProductDto,      // Used to create/update a product
        ProductMapper>,              // The Mapperly mapper
    IProductAppService               // The interface
{
    public ProductAppService(
        IRepository<Product, Guid> repository,
        ProductMapper productMapper)
        : base(repository, productMapper)
    {
        // Configure permissions
        GetPolicyName = YourProjectPermissions.Products.Default;
        GetListPolicyName = YourProjectPermissions.Products.Default;
        CreatePolicyName = YourProjectPermissions.Products.Create;
        UpdatePolicyName = YourProjectPermissions.Products.Edit;
        DeletePolicyName = YourProjectPermissions.Products.Delete;
    }

    // Custom methods can use helper methods from base class
    public async Task<ProductDto> GetEnhancedProductAsync(Guid id)
    {
        var product = await Repository.GetAsync(id);
        return GetMapper().ToEnhancedDto(product);
    }

    public async Task<List<ProductListItemDto>> GetProductListItemsAsync()
    {
        var products = await Repository.GetListAsync();
        return GetMapper().ToListItemDto(products);
    }
}
```

### Benefits

- **Zero code duplication**: Override methods are implemented once in the base class
- **Type safety**: Full compile-time type checking
- **Easy adoption**: Just change the base class inheritance
- **Helper methods**: Convenient methods for custom scenarios
- **ABP compatibility**: Maintains all ABP Framework conventions

### Drawbacks

- **Generic complexity**: More complex generic constraints
- **Inheritance coupling**: Services are coupled to the base class

## Solution 2: Composition with Mapping Service

### Implementation

Create a mapping service that handles all mapping operations:

```csharp
// src/YourProject.Application/Services/IMappingService.cs
public interface IMappingService<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TCreateInput : class
    where TUpdateInput : class
{
    TEntityDto MapToDto(TEntity entity);
    List<TEntityDto> MapToDto(List<TEntity> entities);
    TEntity MapToEntity(TCreateInput createInput);
    void UpdateEntity(TUpdateInput updateInput, TEntity entity);
}

// src/YourProject.Application/Services/MappingService.cs
public class MappingService<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput, TMapper> :
    IMappingService<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TCreateInput : class
    where TUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
{
    private readonly TMapper _mapper;

    public MappingService(TMapper mapper)
    {
        _mapper = mapper;
    }

    public TEntityDto MapToDto(TEntity entity) => _mapper.ToDto(entity);
    public List<TEntityDto> MapToDto(List<TEntity> entities) => _mapper.ToDto(entities);
    public TEntity MapToEntity(TCreateInput createInput) => _mapper.ToEntity(createInput);
    public void UpdateEntity(TUpdateInput updateInput, TEntity entity) => _mapper.UpdateEntity(updateInput, entity);
}
```

### Usage

```csharp
public class ProductAppService : CrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, CreateUpdateProductDto>, IProductAppService
{
    private readonly IMappingService<Product, ProductDto, Guid, CreateUpdateProductDto, CreateUpdateProductDto> _mappingService;

    public ProductAppService(
        IRepository<Product, Guid> repository,
        IMappingService<Product, ProductDto, Guid, CreateUpdateProductDto, CreateUpdateProductDto> mappingService)
        : base(repository)
    {
        _mappingService = mappingService;
    }

    protected override ProductDto MapToGetOutputDto(Product entity) => _mappingService.MapToDto(entity);
    protected override ProductDto MapToGetListOutputDto(Product entity) => _mappingService.MapToDto(entity);
    protected override Product MapToEntity(CreateUpdateProductDto createInput) => _mappingService.MapToEntity(createInput);
    protected override void MapToEntity(CreateUpdateProductDto updateInput, Product entity) => _mappingService.UpdateEntity(updateInput, entity);
}
```

### Benefits

- **Composition over inheritance**: More flexible design
- **Testable**: Easy to mock the mapping service
- **Reusable**: Mapping service can be used in other contexts

### Drawbacks

- **Still some duplication**: Override methods still need to be implemented
- **More complexity**: Additional service layer

## Solution 3: Extension Methods with Reflection

### Implementation

Create extension methods that automatically configure mapping:

```csharp
// src/YourProject.Application/Extensions/CrudAppServiceExtensions.cs
public static class CrudAppServiceExtensions
{
    public static void ConfigureMapperly<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper>(
        this CrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput> service,
        TMapper mapper)
        where TEntity : class, IEntity<TKey>
        where TEntityDto : class, IEntityDto<TKey>
        where TGetListInput : IPagedAndSortedResultRequest
        where TCreateInput : class
        where TUpdateInput : class
        where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
    {
        // Use reflection to override mapping methods
        var serviceType = service.GetType();
        
        // Override MapToGetOutputDto
        var mapToGetOutputDtoMethod = serviceType.GetMethod("MapToGetOutputDto", BindingFlags.NonPublic | BindingFlags.Instance);
        if (mapToGetOutputDtoMethod != null)
        {
            // Create dynamic method that calls mapper.ToDto(entity)
            // This is complex and not recommended for production
        }
    }
}
```

### Benefits

- **Minimal code changes**: Just call the extension method

### Drawbacks

- **Reflection overhead**: Performance impact
- **Complexity**: Hard to maintain and debug
- **Type safety**: Loss of compile-time checking
- **Not recommended**: Too complex for the benefit

## Solution 4: Source Generators (Future Enhancement)

### Concept

Create a source generator that automatically generates the override methods:

```csharp
[GenerateMapperlyCrud]
public partial class ProductAppService : CrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, CreateUpdateProductDto>, IProductAppService
{
    private readonly ProductMapper _mapper;

    public ProductAppService(IRepository<Product, Guid> repository, ProductMapper mapper) : base(repository)
    {
        _mapper = mapper;
    }

    // Override methods are generated automatically by source generator
}
```

### Benefits

- **Zero runtime overhead**: Code generated at compile time
- **Clean syntax**: Minimal boilerplate
- **Type safety**: Full compile-time checking

### Drawbacks

- **Complex implementation**: Requires writing a source generator
- **Development overhead**: Additional tooling required

## Recommendation

**Use Solution 1 (Generic Base Application Service)** as it provides the best balance of:

- **Simplicity**: Easy to understand and implement
- **Performance**: No runtime overhead
- **Type safety**: Full compile-time checking
- **Maintainability**: Clear inheritance hierarchy
- **ABP compatibility**: Works seamlessly with ABP Framework

The implementation is already provided in your project as `MapperlyCrudAppService<>` and eliminates all code duplication while maintaining full ABP Framework compatibility.

## Migration Guide

To migrate existing Application Services to use the new base class:

1. **Change base class**: Replace `CrudAppService<>` with `MapperlyCrudAppService<>`
2. **Add mapper parameter**: Include the mapper in the constructor
3. **Remove override methods**: Delete the duplicate mapping override methods
4. **Use helper methods**: Use `GetMapper()`, `MapEntityToDto()`, etc. for custom scenarios

Example migration:

```csharp
// Before
public class ProductAppService : CrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, CreateUpdateProductDto>
{
    private readonly ProductMapper _mapper;
    
    // ... duplicate override methods ...
}

// After
public class ProductAppService : MapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>
{
    // No duplicate override methods needed!
    // Use GetMapper() for custom scenarios
}
```

This approach provides a clean, maintainable solution that eliminates code duplication while preserving all ABP Framework functionality.
