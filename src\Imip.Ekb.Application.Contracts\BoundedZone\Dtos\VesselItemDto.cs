using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.BoundedZone.Dtos;

public class VesselItemDto : EntityDto<Guid>
{
    public int DocEntry { get; set; }
    public int DocNum { get; set; }
    public string? TenantName { get; set; }
    public string? ItemName { get; set; }
    public decimal? ItemQty { get; set; }
    public string? UnitQty { get; set; }
    public string? Cargo { get; set; }
    public string? Shipment { get; set; }
    public string? Remarks { get; set; }
    public string VesselType { get; set; } = string.Empty; // "Import", "Export", "LocalIn", "LocalOut"
    public TenantShortDto? Tenant { get; set; }
}

public class TenantShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? FullName { get; set; }
}