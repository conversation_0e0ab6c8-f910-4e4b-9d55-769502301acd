using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.PortOfLoading.Dtos;

public class PortOfLoadingDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Deleted { get; set; } = string.Empty;
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Country { get; set; }
    public string DocType { get; set; } = string.Empty;
}