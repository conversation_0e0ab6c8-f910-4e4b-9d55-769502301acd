﻿using Imip.Ekb.Master.Surveyor;
using Imip.Ekb.Master.Surveyor.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class SurveyorMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Surveyor.Id), nameof(SurveyorDto.Id))]
    public partial SurveyorDto MapToDto(Surveyor entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(Surveyor.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(Surveyor.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(Surveyor.CreatedAt))]
    public partial void MapToEntity(SurveyorCreateUpdateDto dto, Surveyor entity);

    // Custom mapping methods for complex scenarios
    public Surveyor CreateEntityWithId(SurveyorCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Surveyor)Activator.CreateInstance(typeof(Surveyor), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<SurveyorDto> MapToDtoList(List<Surveyor> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<SurveyorDto> MapToDtoEnumerable(IEnumerable<Surveyor> entities);
}
