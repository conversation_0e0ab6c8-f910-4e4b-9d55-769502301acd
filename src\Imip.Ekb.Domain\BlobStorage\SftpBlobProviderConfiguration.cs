using System;
using Volo.Abp.BlobStoring;

namespace Imip.Ekb.BlobStorage;

public class SftpBlobProviderConfiguration
{
    /// <summary>
    /// SFTP server host name or IP address.
    /// </summary>
    public string? Host
    {
        get => _containerConfiguration.GetConfiguration<string>("SftpBlobProvider.Host");
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.Host", value);
    }

    /// <summary>
    /// SFTP server port. Default is 22.
    /// </summary>
    public int Port
    {
        get => _containerConfiguration.GetConfigurationOrDefault<int>("SftpBlobProvider.Port", 22);
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.Port", value);
    }

    /// <summary>
    /// Username for SFTP authentication.
    /// </summary>
    public string? UserName
    {
        get => _containerConfiguration.GetConfiguration<string>("SftpBlobProvider.UserName");
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.UserName", value);
    }

    /// <summary>
    /// Password for SFTP authentication.
    /// </summary>
    public string? Password
    {
        get => _containerConfiguration.GetConfiguration<string>("SftpBlobProvider.Password");
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.Password", value);
    }

    /// <summary>
    /// Private key file path for SFTP authentication.
    /// </summary>
    public string? PrivateKeyPath
    {
        get => _containerConfiguration.GetConfiguration<string>("SftpBlobProvider.PrivateKeyPath");
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.PrivateKeyPath", value);
    }

    /// <summary>
    /// Passphrase for the private key if it's encrypted.
    /// </summary>
    public string? PrivateKeyPassphrase
    {
        get => _containerConfiguration.GetConfiguration<string>("SftpBlobProvider.PrivateKeyPassphrase");
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.PrivateKeyPassphrase", value);
    }

    /// <summary>
    /// Base directory on the SFTP server where files will be stored.
    /// </summary>
    public string BaseDirectory
    {
        get
        {
            var value = _containerConfiguration.GetConfigurationOrDefault("SftpBlobProvider.BaseDirectory", "/");
            return value ?? "/";  // Ensure we never return null
        }
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.BaseDirectory", value);
    }

    /// <summary>
    /// Connection timeout in milliseconds. Default is 30000 (30 seconds).
    /// </summary>
    public int ConnectionTimeout
    {
        get => _containerConfiguration.GetConfigurationOrDefault<int>("SftpBlobProvider.ConnectionTimeout", 30000);
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.ConnectionTimeout", value);
    }

    /// <summary>
    /// Operation timeout in milliseconds. Default is 60000 (60 seconds).
    /// </summary>
    public int OperationTimeout
    {
        get => _containerConfiguration.GetConfigurationOrDefault<int>("SftpBlobProvider.OperationTimeout", 60000);
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.OperationTimeout", value);
    }

    /// <summary>
    /// Buffer size for file operations in bytes. Default is 4096 bytes.
    /// </summary>
    public int BufferSize
    {
        get => _containerConfiguration.GetConfigurationOrDefault<int>("SftpBlobProvider.BufferSize", 4096);
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.BufferSize", value);
    }

    /// <summary>
    /// Whether to create directories automatically if they don't exist. Default is true.
    /// </summary>
    public bool CreateDirectoryIfNotExists
    {
        get => _containerConfiguration.GetConfigurationOrDefault<bool>("SftpBlobProvider.CreateDirectoryIfNotExists", true);
        set => _containerConfiguration.SetConfiguration("SftpBlobProvider.CreateDirectoryIfNotExists", value);
    }

    private readonly BlobContainerConfiguration _containerConfiguration;

    public SftpBlobProviderConfiguration(BlobContainerConfiguration containerConfiguration)
    {
        _containerConfiguration = containerConfiguration;
    }
}
